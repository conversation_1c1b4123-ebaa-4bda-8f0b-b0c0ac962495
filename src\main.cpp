// Copyright 2025 <Copyright Owner>

#define SDL_MAIN_HANDLED
#define SPDLOG_NO_EXCEPTIONS
#include "cpu/cpu_diagnostics.h"
#include "gui/backends/imgui_impl_sdl2.h"
#include "gui/backends/imgui_impl_vulkan.h"
#include "gui/game_browser.h"
#include "gui/imgui.h"
#include "gui/imgui_freetype.h"
#include "gui/input_manager.h"
#include "gui/input_settings.h"
#include "gui/performance_overlay.h"
#include "jit/jit_diagnostics.h"
#include "loader/pkg_installer.h"
#include "memory/memory_diagnostics.h"
#include "ps4/ps4_emulator.h"
#include "ps4/ps4_filesystem.h"
#include "ps4/ps4_gpu.h"
#include "video_core/shader_emulator.h"
#include "video_core/tile_manager.h"
#include <SDL2/SDL.h>
#include <algorithm>
#include <chrono>
#include <cstdlib>
#include <cstring>
#include <filesystem>
#include <fstream>
#include <future>
#include <iostream>
#include <memory>
#include <shared_mutex>
#include <spdlog/fmt/fmt.h>
#include <spdlog/spdlog.h>
#include <string>
#include <thread>
#include <vector>

#ifdef _WIN32
#define NOMINMAX
#include <commdlg.h>
#include <shellscalingapi.h>
#include <shlobj.h>
#include <windows.h>
#pragma comment(lib, "Shcore.lib")   // for SetProcessDpiAwareness
#pragma comment(lib, "comdlg32.lib") // for file dialogs
#pragma comment(lib, "shell32.lib")  // for folder browser

static void EnablePerMonitorV2DpiAwareness() {
  if (auto fn = reinterpret_cast<decltype(&SetProcessDpiAwarenessContext)>(
          GetProcAddress(GetModuleHandleA("user32.dll"),
                         "SetProcessDpiAwarenessContext"))) {
    fn(DPI_AWARENESS_CONTEXT_PER_MONITOR_AWARE_V2);
    return;
  }
  SetProcessDpiAwareness(PROCESS_PER_MONITOR_DPI_AWARE);
}
#endif

#ifdef CreateDirectory
#undef CreateDirectory
#endif

#ifdef _WIN32
#else
#include <sys/cdefs.h>
#endif
#include <SDL2/SDL_vulkan.h>
#include <spdlog/sinks/basic_file_sink.h>
#include <spdlog/spdlog.h>

namespace ps4 {

struct EmulatorSettings {
  uint64_t cacheHits = 0;
  uint64_t cacheMisses = 0;
  std::vector<std::string> recent_games;
  std::string last_game_path;
  std::string audio_device = "default";
  std::string gpu_backend = "Vulkan";
  std::string aa_method = "FXAA";
  std::string game_directory = "";
  std::string save_directory = "";
  std::string state_directory = "";
  std::string screenshot_directory = "";
  std::string dump_directory = "";
  std::string log_level = "info";
  std::string network_interface = "auto";
  int width = 1280;
  int height = 720;
  int framerate_limit = 60;
  int auto_save_interval = 300;
  int audio_latency = 50;
  int audio_underrun_warning_interval =
      5; // seconds between underrun warnings (0 = disabled)
  int cpu_thread_count = 8;
  int cache_size_mb = 256;
  int resolution_scale = 1;
  int anisotropy_level = 16;
  int memory_size_gb = 8;
  int swap_size_gb = 4;
  int network_port = 9302;
  float ui_scale = 1.0f;
  float emulation_speed = 1.0f;
  float master_volume = 1.0f;
  float sfx_volume = 1.0f;
  float music_volume = 1.0f;
  float controller_deadzone = 0.1f;
  bool fullscreen = false;
  bool vsync = true;
  bool pause_on_focus_loss = true;
  bool auto_save_states = true;
  bool audio_enabled = true;
  bool jit_enabled = true;
  bool simd_optimizations = true;
  bool branch_prediction = true;
  bool anisotropic_filtering = true;
  bool anti_aliasing = false;
  bool shader_cache = true;
  bool async_shaders = true;
  bool memory_compression = true;
  bool auto_mount_games = true;
  bool debug_mode = false;
  bool show_fps = true;
  bool show_performance = false;
  bool log_syscalls = false;
  bool log_gpu_commands = false;
  bool controller_enabled = true;
  bool keyboard_enabled = true;
  bool mouse_enabled = true;
  bool network_enabled = false;
  bool strict_mode = false;
  bool ignore_missing_imports = false;
  bool patch_games = false;

  void Serialize(std::ostream &out) const {
    out << "version=1\n";
    out << "last_game_path=" << last_game_path << "\n";
    out << "width=" << width << "\n";
    out << "height=" << height << "\n";
    out << "fullscreen=" << (fullscreen ? "1" : "0") << "\n";
    out << "vsync=" << (vsync ? "1" : "0") << "\n";
    out << "framerate_limit=" << framerate_limit << "\n";
    out << "ui_scale=" << ui_scale << "\n";
    out << "emulation_speed=" << emulation_speed << "\n";
    out << "pause_on_focus_loss=" << (pause_on_focus_loss ? "1" : "0") << "\n";
    out << "auto_save_states=" << (auto_save_states ? "1" : "0") << "\n";
    out << "auto_save_interval=" << auto_save_interval << "\n";
    out << "audio_enabled=" << (audio_enabled ? "1" : "0") << "\n";
    out << "master_volume=" << master_volume << "\n";
    out << "sfx_volume=" << sfx_volume << "\n";
    out << "music_volume=" << music_volume << "\n";
    out << "audio_latency=" << audio_latency << "\n";
    out << "audio_underrun_warning_interval=" << audio_underrun_warning_interval
        << "\n";
    out << "audio_device=" << audio_device << "\n";
    out << "cpu_thread_count=" << cpu_thread_count << "\n";
    out << "jit_enabled=" << (jit_enabled ? "1" : "0") << "\n";
    out << "simd_optimizations=" << (simd_optimizations ? "1" : "0") << "\n";
    out << "branch_prediction=" << (branch_prediction ? "1" : "0") << "\n";
    out << "cache_size_mb=" << cache_size_mb << "\n";
    out << "gpu_backend=" << gpu_backend << "\n";
    out << "resolution_scale=" << resolution_scale << "\n";
    out << "anisotropic_filtering=" << (anisotropic_filtering ? "1" : "0")
        << "\n";
    out << "anisotropy_level=" << anisotropy_level << "\n";
    out << "anti_aliasing=" << (anti_aliasing ? "1" : "0") << "\n";
    out << "aa_method=" << aa_method << "\n";
    out << "shader_cache=" << (shader_cache ? "1" : "0") << "\n";
    out << "async_shaders=" << (async_shaders ? "1" : "0") << "\n";
    out << "memory_size_gb=" << memory_size_gb << "\n";
    out << "memory_compression=" << (memory_compression ? "1" : "0") << "\n";
    out << "swap_size_gb=" << swap_size_gb << "\n";
    out << "game_directory=" << game_directory << "\n";
    out << "save_directory=" << save_directory << "\n";
    out << "state_directory=" << state_directory << "\n";
    out << "screenshot_directory=" << screenshot_directory << "\n";
    out << "dump_directory=" << dump_directory << "\n";
    out << "auto_mount_games=" << (auto_mount_games ? "1" : "0") << "\n";
    out << "debug_mode=" << (debug_mode ? "1" : "0") << "\n";
    out << "show_fps=" << (show_fps ? "1" : "0") << "\n";
    out << "show_performance=" << (show_performance ? "1" : "0") << "\n";
    out << "log_syscalls=" << (log_syscalls ? "1" : "0") << "\n";
    out << "log_gpu_commands=" << (log_gpu_commands ? "1" : "0") << "\n";
    out << "log_level=" << log_level << "\n";
    out << "controller_enabled=" << (controller_enabled ? "1" : "0") << "\n";
    out << "controller_deadzone=" << controller_deadzone << "\n";
    out << "keyboard_enabled=" << (keyboard_enabled ? "1" : "0") << "\n";
    out << "mouse_enabled=" << (mouse_enabled ? "1" : "0") << "\n";
    out << "network_enabled=" << (network_enabled ? "1" : "0") << "\n";
    out << "network_interface=" << network_interface << "\n";
    out << "network_port=" << network_port << "\n";
    out << "strict_mode=" << (strict_mode ? "1" : "0") << "\n";
    out << "ignore_missing_imports=" << (ignore_missing_imports ? "1" : "0")
        << "\n";
    out << "patch_games=" << (patch_games ? "1" : "0") << "\n";
    out << "recent_game_count=" << recent_games.size() << "\n";
    for (size_t i = 0; i < recent_games.size(); ++i) {
      out << "recent_game" << i << "=" << recent_games[i] << "\n";
    }
    out << "cacheHits=" << cacheHits << "\n";
    out << "cacheMisses=" << cacheMisses << "\n";
  }

  void Deserialize(std::istream &in) {
    std::string line;
    size_t recent_game_count = 0;
    uint32_t loaded_version_from_file = 0;

    while (std::getline(in, line)) {
      auto pos = line.find('=');
      if (pos == std::string::npos)
        continue;
      auto key = line.substr(0, pos);
      auto value = line.substr(pos + 1);
      try {
        if (key == "version")
          loaded_version_from_file = std::stoul(value);
        else if (key == "last_game_path")
          last_game_path = value;
        else if (key == "width")
          width = std::stoi(value);
        else if (key == "height")
          height = std::stoi(value);
        else if (key == "fullscreen")
          fullscreen = (value == "1");
        else if (key == "vsync")
          vsync = (value == "1");
        else if (key == "framerate_limit")
          framerate_limit = std::stoi(value);
        else if (key == "ui_scale")
          ui_scale = std::stof(value);
        else if (key == "emulation_speed")
          emulation_speed = std::stof(value);
        else if (key == "pause_on_focus_loss")
          pause_on_focus_loss = (value == "1");
        else if (key == "auto_save_states")
          auto_save_states = (value == "1");
        else if (key == "auto_save_interval")
          auto_save_interval = std::stoi(value);
        else if (key == "audio_enabled")
          audio_enabled = (value == "1");
        else if (key == "master_volume")
          master_volume = std::stof(value);
        else if (key == "sfx_volume")
          sfx_volume = std::stof(value);
        else if (key == "music_volume")
          music_volume = std::stof(value);
        else if (key == "audio_latency")
          audio_latency = std::stoi(value);
        else if (key == "audio_underrun_warning_interval")
          audio_underrun_warning_interval = std::stoi(value);
        else if (key == "audio_device")
          audio_device = value;
        else if (key == "cpu_thread_count")
          cpu_thread_count = std::stoi(value);
        else if (key == "jit_enabled")
          jit_enabled = (value == "1");
        else if (key == "simd_optimizations")
          simd_optimizations = (value == "1");
        else if (key == "branch_prediction")
          branch_prediction = (value == "1");
        else if (key == "cache_size_mb")
          cache_size_mb = std::stoi(value);
        else if (key == "gpu_backend")
          gpu_backend = value;
        else if (key == "resolution_scale")
          resolution_scale = std::stoi(value);
        else if (key == "anisotropic_filtering")
          anisotropic_filtering = (value == "1");
        else if (key == "anisotropy_level")
          anisotropy_level = std::stoi(value);
        else if (key == "anti_aliasing")
          anti_aliasing = (value == "1");
        else if (key == "aa_method")
          aa_method = value;
        else if (key == "shader_cache")
          shader_cache = (value == "1");
        else if (key == "async_shaders")
          async_shaders = (value == "1");
        else if (key == "memory_size_gb")
          memory_size_gb = std::stoi(value);
        else if (key == "memory_compression")
          memory_compression = (value == "1");
        else if (key == "swap_size_gb")
          swap_size_gb = std::stoi(value);
        else if (key == "game_directory")
          game_directory = value;
        else if (key == "save_directory")
          save_directory = value;
        else if (key == "state_directory")
          state_directory = value;
        else if (key == "screenshot_directory")
          screenshot_directory = value;
        else if (key == "dump_directory")
          dump_directory = value;
        else if (key == "auto_mount_games")
          auto_mount_games = (value == "1");
        else if (key == "debug_mode")
          debug_mode = (value == "1");
        else if (key == "show_fps")
          show_fps = (value == "1");
        else if (key == "show_performance")
          show_performance = (value == "1");
        else if (key == "log_syscalls")
          log_syscalls = (value == "1");
        else if (key == "log_gpu_commands")
          log_gpu_commands = (value == "1");
        else if (key == "log_level")
          log_level = value;
        else if (key == "controller_enabled")
          controller_enabled = (value == "1");
        else if (key == "controller_deadzone")
          controller_deadzone = std::stof(value);
        else if (key == "keyboard_enabled")
          keyboard_enabled = (value == "1");
        else if (key == "mouse_enabled")
          mouse_enabled = (value == "1");
        else if (key == "network_enabled")
          network_enabled = (value == "1");
        else if (key == "network_interface")
          network_interface = value;
        else if (key == "network_port")
          network_port = std::stoi(value);
        else if (key == "strict_mode")
          strict_mode = (value == "1");
        else if (key == "ignore_missing_imports")
          ignore_missing_imports = (value == "1");
        else if (key == "patch_games")
          patch_games = (value == "1");
        else if (key == "recent_game_count")
          recent_game_count = std::stoul(value);
        else if (key.rfind("recent_game", 0) == 0)
          recent_games.push_back(value);
        else if (key == "cacheHits")
          cacheHits = std::stoull(value);
        else if (key == "cacheMisses")
          cacheMisses = std::stoull(value);
      } catch (const std::exception &e) {
        spdlog::error("Settings::Deserialize failed for key {}: {}", key,
                      e.what());
        throw std::runtime_error("Invalid settings format");
      }
    }
    if (loaded_version_from_file == 0) {
      spdlog::info("No settings version found, creating new settings file with "
                   "version 1");
    } else if (loaded_version_from_file != 1) {
      spdlog::error("Unsupported settings version: {}",
                    loaded_version_from_file);
      throw std::runtime_error("Unsupported settings version");
    }
    if (recent_games.size() > recent_game_count) {
      recent_games.resize(recent_game_count);
    }
  }
};

static constexpr int kDefaultWidth = 1280;
static constexpr int kDefaultHeight = 720;
static constexpr const char *kWindowTitle =
    "PS4 Emulator - Advanced Configuration";
static EmulatorSettings settings;
static char gamePathBuf[260] = "";
static char statePathBuf[260] = "";
static bool show_load_game_window = false;
static bool show_save_state_window = false;
static bool show_load_state_window = false;
static bool show_filesystem_window = false;
static bool show_diagnostics_window = false;
static bool show_input_window = false;
static bool show_font_debug_window = false;
static bool show_install_pkg_window = false;
static bool show_set_game_directory_window = false;
static bool show_display_settings = false;
static bool show_audio_settings = false;
static bool show_cpu_settings = false;
static bool show_gpu_settings = false;
static bool show_memory_settings = false;
static bool show_filesystem_settings = false;
static bool show_debug_settings = false;
static bool show_input_settings = false;
static bool show_network_settings = false;
static bool show_compatibility_settings = false;
static bool show_advanced_settings = false;
static bool show_game_browser = false;
static std::vector<std::string> game_list;
static std::string selected_game = "";
static bool show_performance_overlay = false;
static std::shared_mutex settingsMutex;
static std::string globalErrorMessage = ""; // For centralized error display

static ps4::GameBrowser *gameBrowser = nullptr;
static ps4::InputSettings *inputSettings = nullptr;
static ps4::PerformanceOverlay *performanceOverlay = nullptr;
static SDL_Window *sdl_window_ptr = nullptr; // Pointer to the SDL window

static std::filesystem::path getSettingsPath() {
  auto env = std::getenv("APPDATA");
  std::filesystem::path dir = env ? env : std::filesystem::current_path();
  dir /= "PS4Emulator";
  try {
    std::filesystem::create_directories(dir);
  } catch (const std::exception &e) {
    spdlog::error("Failed to create settings directory {}: {}", dir.string(),
                  e.what());
    globalErrorMessage =
        fmt::format("Failed to create settings directory: {}", e.what());
  }
  return dir / "settings.ini";
}

static void LoadSettings() {
  auto start = std::chrono::high_resolution_clock::now();
  std::unique_lock<std::shared_mutex> lock(settingsMutex);
  try {
    auto path = getSettingsPath();
    std::ifstream in(path);
    if (in) {
      settings.Deserialize(in);
      settings.cacheHits++;
      auto end = std::chrono::high_resolution_clock::now();
      auto latency =
          std::chrono::duration_cast<std::chrono::microseconds>(end - start)
              .count();
      spdlog::info("LoadSettings: Loaded from {}, latency={}us", path.string(),
                   latency);
    } else {
      settings.cacheMisses++;
      spdlog::warn("LoadSettings: No settings file found at {}", path.string());
    }
  } catch (const std::exception &e) {
    settings.cacheMisses++;
    spdlog::error("LoadSettings failed: {}", e.what());
    globalErrorMessage = fmt::format("Failed to load settings: {}", e.what());
  }
}

static void SaveSettings() {
  auto start = std::chrono::high_resolution_clock::now();
  std::unique_lock<std::shared_mutex> lock(settingsMutex);
  try {
    auto path = getSettingsPath();
    auto tmp = path;
    tmp += ".tmp";
    std::ofstream out(tmp);
    settings.Serialize(out);
    out.close();
    std::filesystem::rename(tmp, path);
    settings.cacheHits++;
    auto end = std::chrono::high_resolution_clock::now();
    auto latency =
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    spdlog::info("SaveSettings: Saved to {}, latency={}us", path.string(),
                 latency);
  } catch (const std::exception &e) {
    settings.cacheMisses++;
    spdlog::error("SaveSettings failed: {}", e.what());
    globalErrorMessage = fmt::format("Failed to save settings: {}", e.what());
  }
}

// Helper function to open a file dialog for selecting a game
static std::string OpenFileDialog(const char *filter, const char *title) {
  char filePath[1024] = "";
#ifdef _WIN32
  OPENFILENAMEA ofn;
  ZeroMemory(&ofn, sizeof(ofn));
  ofn.lStructSize = sizeof(ofn);
  ofn.lpstrFile = filePath;
  ofn.nMaxFile = sizeof(filePath);
  ofn.lpstrFilter = filter;
  ofn.nFilterIndex = 1;
  ofn.lpstrFileTitle = NULL;
  ofn.nMaxFileTitle = 0;
  ofn.lpstrInitialDir = NULL;
  ofn.Flags = OFN_PATHMUSTEXIST | OFN_FILEMUSTEXIST | OFN_NOCHANGEDIR;
  ofn.lpstrTitle = title;

  if (GetOpenFileNameA(&ofn)) {
    return filePath;
  }
#else
  // For Linux/macOS, you might use a library like tinyfiledialogs or a system
  // command For simplicity, this example only provides a placeholder.
  spdlog::warn("File dialog not implemented for non-Windows platforms.");
  globalErrorMessage = "File dialog not implemented for non-Windows platforms.";
#endif
  return "";
}

// Helper function to open a folder dialog
static std::string OpenFolderDialog(const char *title) {
  char folderPath[1024] = "";
#ifdef _WIN32
  BROWSEINFOA bi = {0};
  bi.lpszTitle = title;
  bi.ulFlags = BIF_RETURNONLYFSDIRS | BIF_NEWDIALOGSTYLE;
  LPITEMIDLIST pidl = SHBrowseForFolderA(&bi);
  if (pidl != 0) {
    SHGetPathFromIDListA(pidl, folderPath);
    IMalloc *imalloc = 0;
    if (SUCCEEDED(SHGetMalloc(&imalloc))) {
      imalloc->Free(pidl);
      imalloc->Release();
    }
    return folderPath;
  }
#else
  spdlog::warn("Folder dialog not implemented for non-Windows platforms.");
  globalErrorMessage =
      "Folder dialog not implemented for non-Windows platforms.";
#endif
  return "";
}

static void check_vk_result(VkResult err) {
  if (err == VK_SUCCESS)
    return;
  spdlog::error("[vulkan] Error: VkResult = {}", static_cast<int>(err));
  globalErrorMessage =
      fmt::format("Vulkan Error: VkResult = {}", static_cast<int>(err));
  if (err < 0)
    throw std::runtime_error("Vulkan critical error");
}

void renderFrame(VulkanContext &vk_context,
                 VkCommandBuffer render_command_buffer, ImDrawData *draw_data,
                 uint32_t &image_index, bool &swapchain_rebuild, bool &quit,
                 PS4Emulator &emulator) {
  auto start = std::chrono::high_resolution_clock::now();
  ImVec4 clear_color = ImVec4(0.12f, 0.12f, 0.12f, 1.00f);
  std::unique_lock<std::shared_mutex> lock(vk_context.contextMutex);
  try {
    vkWaitForFences(vk_context.device, 1, &vk_context.inFlightFence, VK_TRUE,
                    UINT64_MAX);
    VkResult result = vkAcquireNextImageKHR(
        vk_context.device, vk_context.swapchain, UINT64_MAX,
        vk_context.imageAvailableSemaphore, VK_NULL_HANDLE, &image_index);
    if (result == VK_ERROR_OUT_OF_DATE_KHR || result == VK_SUBOPTIMAL_KHR) {
      swapchain_rebuild = true;
      vk_context.cacheHits++;
      return;
    } else if (result != VK_SUCCESS) {
      vk_context.cacheMisses++;
      spdlog::error(fmt::format("Failed to acquire swapchain image: {}",
                                static_cast<int>(result)));
      globalErrorMessage = fmt::format("Failed to acquire swapchain image: {}",
                                       static_cast<int>(result));
      quit = true;
      return;
    }
    vkResetFences(vk_context.device, 1, &vk_context.inFlightFence);
    vkResetCommandBuffer(render_command_buffer, 0);
    VkCommandBufferBeginInfo begin_info = {
        VK_STRUCTURE_TYPE_COMMAND_BUFFER_BEGIN_INFO};
    if (vkBeginCommandBuffer(render_command_buffer, &begin_info) !=
        VK_SUCCESS) {
      vk_context.cacheMisses++;
      spdlog::error(fmt::format("Failed to begin recording command buffer"));
      globalErrorMessage = "Failed to begin recording command buffer";
      quit = true;
      return;
    }
    VkClearValue clear_value = {};
    clear_value.color.float32[0] = clear_color.x * clear_color.w;
    clear_value.color.float32[1] = clear_color.y * clear_color.w;
    clear_value.color.float32[2] = clear_color.z * clear_color.w;
    clear_value.color.float32[3] = clear_color.w;
    VkRenderPassBeginInfo render_pass_info = {
        VK_STRUCTURE_TYPE_RENDER_PASS_BEGIN_INFO,
        nullptr,
        vk_context.renderPass,
        vk_context.framebuffers[image_index],
        {{0, 0}, vk_context.swapchainExtent},
        1,
        &clear_value};
    vkCmdBeginRenderPass(render_command_buffer, &render_pass_info,
                         VK_SUBPASS_CONTENTS_INLINE);
    ImGui_ImplVulkan_RenderDrawData(draw_data, render_command_buffer);
    vkCmdEndRenderPass(render_command_buffer);
    if (vkEndCommandBuffer(render_command_buffer) != VK_SUCCESS) {
      vk_context.cacheMisses++;
      spdlog::error(fmt::format("Failed to record command buffer"));
      globalErrorMessage = "Failed to record command buffer";
      quit = true;
      return;
    }
    VkPipelineStageFlags wait_stages[] = {
        VK_PIPELINE_STAGE_COLOR_ATTACHMENT_OUTPUT_BIT};
    VkSubmitInfo submit_info = {VK_STRUCTURE_TYPE_SUBMIT_INFO,
                                nullptr,
                                1,
                                &vk_context.imageAvailableSemaphore,
                                wait_stages,
                                1,
                                &render_command_buffer,
                                1,
                                &vk_context.renderFinishedSemaphore};
    if (vkQueueSubmit(vk_context.graphicsQueue, 1, &submit_info,
                      vk_context.inFlightFence) != VK_SUCCESS) {
      vk_context.cacheMisses++;
      spdlog::error(fmt::format("Failed to submit draw command buffer"));
      globalErrorMessage = "Failed to submit draw command buffer";
      quit = true;
      return;
    }
    VkPresentInfoKHR present_info = {VK_STRUCTURE_TYPE_PRESENT_INFO_KHR,
                                     nullptr,
                                     1,
                                     &vk_context.renderFinishedSemaphore,
                                     1,
                                     &vk_context.swapchain,
                                     &image_index,
                                     nullptr};
    result = vkQueuePresentKHR(vk_context.graphicsQueue, &present_info);
    if (result == VK_ERROR_OUT_OF_DATE_KHR || result == VK_SUBOPTIMAL_KHR) {
      swapchain_rebuild = true;
    } else if (result != VK_SUCCESS) {
      vk_context.cacheMisses++;
      spdlog::error(fmt::format("Failed to present swapchain image: {}",
                                static_cast<int>(result)));
      globalErrorMessage = fmt::format("Failed to present swapchain image: {}",
                                       static_cast<int>(result));
      quit = true;
    }
    vk_context.cacheHits++;
    vk_context.frameCount++;
    auto end = std::chrono::high_resolution_clock::now();
    auto latency =
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    vk_context.renderLatencyUs += latency;
    spdlog::trace(fmt::format("renderFrame: frame={}, latency={}us",
                              vk_context.frameCount, latency));

    // Performance Optimization: This is where rendering optimizations would
    // typically go. For example, batching draw calls, optimizing shader usage,
    // reducing redundant state changes. This requires deep profiling and
    // specific knowledge of the rendering workload.

  } catch (const std::exception &e) {
    vk_context.cacheMisses++;
    spdlog::error("renderFrame failed: {}", e.what());
    globalErrorMessage = fmt::format("Rendering failed: {}", e.what());
    quit = true;
  }
}

void createSwapchain(VulkanContext &vk_context, SDL_Window *window) {
  VkSurfaceCapabilitiesKHR capabilities;
  vkGetPhysicalDeviceSurfaceCapabilitiesKHR(vk_context.physicalDevice,
                                            vk_context.surface, &capabilities);

  uint32_t formatCount;
  vkGetPhysicalDeviceSurfaceFormatsKHR(
      vk_context.physicalDevice, vk_context.surface, &formatCount, nullptr);
  std::vector<VkSurfaceFormatKHR> formats(formatCount);
  vkGetPhysicalDeviceSurfaceFormatsKHR(vk_context.physicalDevice,
                                       vk_context.surface, &formatCount,
                                       formats.data());

  if (formats.empty()) {
    spdlog::error("No surface formats available from Vulkan driver");
    globalErrorMessage = "No surface formats available from Vulkan driver.";
    throw std::runtime_error("No Vulkan surface formats available");
  }

  VkSurfaceFormatKHR surfaceFormat = formats[0];
  for (const auto &format : formats) {
    if (format.format == VK_FORMAT_B8G8R8A8_SRGB &&
        format.colorSpace == VK_COLOR_SPACE_SRGB_NONLINEAR_KHR) {
      surfaceFormat = format;
      break;
    }
    if (format.format == VK_FORMAT_B8G8R8A8_UNORM &&
        format.colorSpace == VK_COLOR_SPACE_SRGB_NONLINEAR_KHR) {
      surfaceFormat = format;
    }
  }

  uint32_t presentModeCount;
  vkGetPhysicalDeviceSurfacePresentModesKHR(vk_context.physicalDevice,
                                            vk_context.surface,
                                            &presentModeCount, nullptr);
  std::vector<VkPresentModeKHR> presentModes(presentModeCount);
  vkGetPhysicalDeviceSurfacePresentModesKHR(
      vk_context.physicalDevice, vk_context.surface, &presentModeCount,
      presentModes.data());
  VkPresentModeKHR presentMode = VK_PRESENT_MODE_FIFO_KHR;

  int width, height;
  SDL_Vulkan_GetDrawableSize(window, &width, &height);
  VkExtent2D extent = {static_cast<uint32_t>(width),
                       static_cast<uint32_t>(height)};
  extent.width =
      std::max(capabilities.minImageExtent.width,
               std::min(capabilities.maxImageExtent.width, extent.width));
  extent.height =
      std::max(capabilities.minImageExtent.height,
               std::min(capabilities.maxImageExtent.height, extent.height));

  VkSwapchainCreateInfoKHR swapchainInfo = {};
  swapchainInfo.sType = VK_STRUCTURE_TYPE_SWAPCHAIN_CREATE_INFO_KHR;
  swapchainInfo.surface = vk_context.surface;
  swapchainInfo.minImageCount = capabilities.minImageCount + 1;
  if (capabilities.maxImageCount > 0 &&
      swapchainInfo.minImageCount > capabilities.maxImageCount) {
    swapchainInfo.minImageCount = capabilities.maxImageCount;
  }
  swapchainInfo.imageFormat = surfaceFormat.format;
  swapchainInfo.imageColorSpace = surfaceFormat.colorSpace;
  swapchainInfo.imageExtent = extent;
  swapchainInfo.imageArrayLayers = 1;
  swapchainInfo.imageUsage = VK_IMAGE_USAGE_COLOR_ATTACHMENT_BIT;
  swapchainInfo.preTransform = capabilities.currentTransform;
  swapchainInfo.compositeAlpha = VK_COMPOSITE_ALPHA_OPAQUE_BIT_KHR;
  swapchainInfo.presentMode = presentMode;
  swapchainInfo.clipped = VK_TRUE;
  swapchainInfo.oldSwapchain = VK_NULL_HANDLE;

  if (vkCreateSwapchainKHR(vk_context.device, &swapchainInfo, nullptr,
                           &vk_context.swapchain) != VK_SUCCESS) {
    globalErrorMessage = "Failed to create swapchain";
    throw std::runtime_error("Failed to create swapchain");
  }

  uint32_t imageCount;
  vkGetSwapchainImagesKHR(vk_context.device, vk_context.swapchain, &imageCount,
                          nullptr);
  vk_context.swapchainImages.resize(imageCount);
  vkGetSwapchainImagesKHR(vk_context.device, vk_context.swapchain, &imageCount,
                          vk_context.swapchainImages.data());

  vk_context.swapchainImageViews.resize(imageCount);
  for (size_t i = 0; i < imageCount; i++) {
    VkImageViewCreateInfo viewInfo = {};
    viewInfo.sType = VK_STRUCTURE_TYPE_IMAGE_VIEW_CREATE_INFO;
    viewInfo.image = vk_context.swapchainImages[i];
    viewInfo.viewType = VK_IMAGE_VIEW_TYPE_2D;
    viewInfo.format = surfaceFormat.format;
    viewInfo.components.r = VK_COMPONENT_SWIZZLE_IDENTITY;
    viewInfo.components.g = VK_COMPONENT_SWIZZLE_IDENTITY;
    viewInfo.components.b = VK_COMPONENT_SWIZZLE_IDENTITY;
    viewInfo.components.a = VK_COMPONENT_SWIZZLE_IDENTITY;
    viewInfo.subresourceRange.aspectMask = VK_IMAGE_ASPECT_COLOR_BIT;
    viewInfo.subresourceRange.baseMipLevel = 0;
    viewInfo.subresourceRange.levelCount = 1;
    viewInfo.subresourceRange.baseArrayLayer = 0;
    viewInfo.subresourceRange.layerCount = 1;

    if (vkCreateImageView(vk_context.device, &viewInfo, nullptr,
                          &vk_context.swapchainImageViews[i]) != VK_SUCCESS) {
      globalErrorMessage = "Failed to create image view";
      throw std::runtime_error("Failed to create image view");
    }
  }

  vk_context.swapchainExtent = extent;
  vk_context.swapchainImageFormat = surfaceFormat.format;
}

void createRenderPass(VulkanContext &vk_context) {
  VkAttachmentDescription colorAttachment = {};
  colorAttachment.format = vk_context.swapchainImageFormat;
  colorAttachment.samples = VK_SAMPLE_COUNT_1_BIT;
  colorAttachment.loadOp = VK_ATTACHMENT_LOAD_OP_CLEAR;
  colorAttachment.storeOp = VK_ATTACHMENT_STORE_OP_STORE;
  colorAttachment.stencilLoadOp = VK_ATTACHMENT_LOAD_OP_DONT_CARE;
  colorAttachment.stencilStoreOp = VK_ATTACHMENT_STORE_OP_DONT_CARE;
  colorAttachment.initialLayout = VK_IMAGE_LAYOUT_UNDEFINED;
  colorAttachment.finalLayout = VK_IMAGE_LAYOUT_PRESENT_SRC_KHR;

  VkAttachmentReference colorAttachmentRef = {};
  colorAttachmentRef.attachment = 0;
  colorAttachmentRef.layout = VK_IMAGE_LAYOUT_COLOR_ATTACHMENT_OPTIMAL;

  VkSubpassDescription subpass = {};
  subpass.pipelineBindPoint = VK_PIPELINE_BIND_POINT_GRAPHICS;
  subpass.colorAttachmentCount = 1;
  subpass.pColorAttachments = &colorAttachmentRef;

  VkRenderPassCreateInfo renderPassInfo = {};
  renderPassInfo.sType = VK_STRUCTURE_TYPE_RENDER_PASS_CREATE_INFO;
  renderPassInfo.attachmentCount = 1;
  renderPassInfo.pAttachments = &colorAttachment;
  renderPassInfo.subpassCount = 1;
  renderPassInfo.pSubpasses = &subpass;

  if (vkCreateRenderPass(vk_context.device, &renderPassInfo, nullptr,
                         &vk_context.renderPass) != VK_SUCCESS) {
    globalErrorMessage = "Failed to create render pass";
    throw std::runtime_error("Failed to create render pass");
  }
}

void createFramebuffers(VulkanContext &vk_context) {
  vk_context.framebuffers.resize(vk_context.swapchainImageViews.size());
  for (size_t i = 0; i < vk_context.swapchainImageViews.size(); i++) {
    VkImageView attachments[] = {vk_context.swapchainImageViews[i]};

    VkFramebufferCreateInfo framebufferInfo = {};
    framebufferInfo.sType = VK_STRUCTURE_TYPE_FRAMEBUFFER_CREATE_INFO;
    framebufferInfo.renderPass = vk_context.renderPass;
    framebufferInfo.attachmentCount = 1;
    framebufferInfo.pAttachments = attachments;
    framebufferInfo.width = vk_context.swapchainExtent.width;
    framebufferInfo.height = vk_context.swapchainExtent.height;
    framebufferInfo.layers = 1;

    if (vkCreateFramebuffer(vk_context.device, &framebufferInfo, nullptr,
                            &vk_context.framebuffers[i]) != VK_SUCCESS) {
      globalErrorMessage = "Failed to create framebuffer";
      throw std::runtime_error("Failed to create framebuffer");
    }
  }
}

void createDescriptorPool(VulkanContext &vk_context) {
  VkDescriptorPoolSize poolSizes[] = {
      {VK_DESCRIPTOR_TYPE_SAMPLER, 1000},
      {VK_DESCRIPTOR_TYPE_COMBINED_IMAGE_SAMPLER, 1000},
      {VK_DESCRIPTOR_TYPE_SAMPLED_IMAGE, 1000},
      {VK_DESCRIPTOR_TYPE_STORAGE_IMAGE, 1000},
      {VK_DESCRIPTOR_TYPE_UNIFORM_TEXEL_BUFFER, 1000},
      {VK_DESCRIPTOR_TYPE_STORAGE_TEXEL_BUFFER, 1000},
      {VK_DESCRIPTOR_TYPE_UNIFORM_BUFFER, 1000},
      {VK_DESCRIPTOR_TYPE_STORAGE_BUFFER, 1000},
      {VK_DESCRIPTOR_TYPE_UNIFORM_BUFFER_DYNAMIC, 1000},
      {VK_DESCRIPTOR_TYPE_STORAGE_BUFFER_DYNAMIC, 1000},
      {VK_DESCRIPTOR_TYPE_INPUT_ATTACHMENT, 1000}};

  VkDescriptorPoolCreateInfo poolInfo = {};
  poolInfo.sType = VK_STRUCTURE_TYPE_DESCRIPTOR_POOL_CREATE_INFO;
  poolInfo.flags = VK_DESCRIPTOR_POOL_CREATE_FREE_DESCRIPTOR_SET_BIT;
  poolInfo.maxSets = 1000 * IM_ARRAYSIZE(poolSizes);
  poolInfo.poolSizeCount = IM_ARRAYSIZE(poolSizes);
  poolInfo.pPoolSizes = poolSizes;

  if (vkCreateDescriptorPool(vk_context.device, &poolInfo, nullptr,
                             &vk_context.descriptorPool) != VK_SUCCESS) {
    globalErrorMessage = "Failed to create descriptor pool";
    throw std::runtime_error("Failed to create descriptor pool");
  }
}

void cleanupSwapchain(VulkanContext &vk_context) {
  for (auto framebuffer : vk_context.framebuffers) {
    vkDestroyFramebuffer(vk_context.device, framebuffer, nullptr);
  }
  for (auto imageView : vk_context.swapchainImageViews) {
    vkDestroyImageView(vk_context.device, imageView, nullptr);
  }
  vkDestroySwapchainKHR(vk_context.device, vk_context.swapchain, nullptr);
}

void cleanupVulkan(VulkanContext &vk_context) {
  vkDeviceWaitIdle(vk_context.device);
  cleanupSwapchain(vk_context);
  vkDestroyRenderPass(vk_context.device, vk_context.renderPass, nullptr);
  vkDestroyDescriptorPool(vk_context.device, vk_context.descriptorPool,
                          nullptr);
  vkDestroyCommandPool(vk_context.device, vk_context.commandPool, nullptr);
  vkDestroyFence(vk_context.device, vk_context.inFlightFence, nullptr);
  vkDestroySemaphore(vk_context.device, vk_context.renderFinishedSemaphore,
                     nullptr);
  vkDestroySemaphore(vk_context.device, vk_context.imageAvailableSemaphore,
                     nullptr);
  vkDestroyDevice(vk_context.device, nullptr);
  vkDestroySurfaceKHR(vk_context.instance, vk_context.surface, nullptr);
  vkDestroyInstance(vk_context.instance, nullptr);
}

// --- Settings Application Functions ---
static void ApplyDisplaySettings(SDL_Window *window) {
  SDL_SetWindowSize(window, settings.width, settings.height);
  SDL_SetWindowFullscreen(
      window, settings.fullscreen ? SDL_WINDOW_FULLSCREEN_DESKTOP : 0);
  // Re-apply UI scale and font settings
  ImGuiIO &io = ImGui::GetIO();
  float ddpi, hdpi, vdpi;
  float dpi_scale = 1.0f;
  int display_index = SDL_GetWindowDisplayIndex(window);
  if (SDL_GetDisplayDPI(display_index, &ddpi, &hdpi, &vdpi) == 0) {
    dpi_scale = ddpi / 96.0f;
  }
  dpi_scale = std::clamp(dpi_scale, 0.75f, 3.0f);
  float base_font_size = 16.0f * settings.ui_scale;
  float final_scale = dpi_scale * settings.ui_scale;

  ImFontConfig font_cfg;
  font_cfg.SizePixels = base_font_size;
  font_cfg.OversampleH = 3;
  font_cfg.OversampleV = 2;
  font_cfg.RasterizerDensity = final_scale;

#ifdef IMGUI_ENABLE_FREETYPE
  font_cfg.FontBuilderFlags = ImGuiFreeTypeBuilderFlags_LightHinting;
#endif
  io.Fonts->Clear();
  ImFont *primaryFont = nullptr;
  const char *fontPaths[] = {
      "d:/sss/Roboto-Black.ttf", "C:/Windows/Fonts/segoeui.ttf",
      "C:/Windows/Fonts/arial.ttf", "C:/Windows/Fonts/calibri.ttf", nullptr};
  for (const char **path = fontPaths; *path != nullptr; ++path) {
    if (std::filesystem::exists(*path)) {
      primaryFont =
          io.Fonts->AddFontFromFileTTF(*path, base_font_size, &font_cfg);
      if (primaryFont) {
        break;
      }
    }
  }
  if (!primaryFont) {
    primaryFont = io.Fonts->AddFontDefault(
        &font_cfg); // Fallback to default if no TTF found
  }
  io.FontDefault = primaryFont;
  io.DisplayFramebufferScale = ImVec2(final_scale, final_scale);
  io.Fonts->Build();
  ImGui::GetStyle().ScaleAllSizes(final_scale);
  spdlog::info("Applied display settings: width={}, height={}, fullscreen={}, "
               "ui_scale={}",
               settings.width, settings.height, settings.fullscreen,
               settings.ui_scale);
}

static void ApplyAudioSettings(PS4Emulator *emulator) {
  if (emulator) {
    emulator->GetAudio().SetEnabled(settings.audio_enabled);
    emulator->GetAudio().SetUnderrunWarningInterval(
        settings.audio_underrun_warning_interval);
    // Additional audio settings like volume, device, latency would be applied
    // here if the Audio class had corresponding setters.
    spdlog::info("Applied audio settings: enabled={}, master_volume={}, "
                 "underrun_warning_interval={}s",
                 settings.audio_enabled, settings.master_volume,
                 settings.audio_underrun_warning_interval);
  }
}

// Function to get audio enabled setting for use by emulator
bool IsAudioEnabled() {
  std::shared_lock<std::shared_mutex> lock(settingsMutex);

  // Check for environment variable override to disable audio
  const char *disableAudio = std::getenv("PS4EMU_DISABLE_AUDIO");
  if (disableAudio && (std::string(disableAudio) == "1" ||
                       std::string(disableAudio) == "true")) {
    spdlog::info("Audio disabled by environment variable PS4EMU_DISABLE_AUDIO");
    return false;
  }

  return settings.audio_enabled;
}

static void ApplyCPUSettings(PS4Emulator *emulator) {
  if (emulator) {
    // These settings often require re-initialization of CPU/JIT,
    // which might not be possible during runtime.
    // For now, just log that they are "applied".
    spdlog::info(
        "Applied CPU settings: threads={}, jit_enabled={}, "
        "simd_optimizations={}, branch_prediction={}, cache_size_mb={}",
        settings.cpu_thread_count, settings.jit_enabled,
        settings.simd_optimizations, settings.branch_prediction,
        settings.cache_size_mb);
    spdlog::warn("CPU settings like thread count, JIT, SIMD, branch prediction "
                 "usually require emulator restart to take full effect.");
  }
}

static void ApplyGPUSettings(PS4Emulator *emulator) {
  if (emulator) {
    // GPU backend change usually requires re-initialization of Vulkan context.
    // Other settings like resolution scale, anisotropic filtering, AA can be
    // applied dynamically.
    spdlog::info("Applied GPU settings: backend={}, resolution_scale={}, "
                 "anisotropic_filtering={}, anti_aliasing={}",
                 settings.gpu_backend, settings.resolution_scale,
                 settings.anisotropic_filtering, settings.anti_aliasing);
    spdlog::warn("Changing GPU backend usually requires emulator restart.");
  }
}

static void ApplyMemorySettings(PS4Emulator *emulator) {
  if (emulator) {
    // Memory settings often require re-initialization of memory manager.
    spdlog::info(
        "Applied memory settings: size_gb={}, compression={}, swap_size_gb={}",
        settings.memory_size_gb, settings.memory_compression,
        settings.swap_size_gb);
    spdlog::warn("Memory settings usually require emulator restart to take "
                 "full effect.");
  }
}

static void ApplyFilesystemSettings(PS4Emulator *emulator) {
  if (emulator) {
    // Assuming GetFilesystem().SetSettings() applies the changes
    // For simplicity, this example doesn't pass the full fsSettings struct.
    // In a real scenario, you'd pass the updated fsSettings struct to the
    // filesystem.
    spdlog::info(
        "Applied filesystem settings: game_directory={}, auto_mount_games={}",
        settings.game_directory, settings.auto_mount_games);
    emulator->GetFilesystem().SetGameDirectory(settings.game_directory);
  }
}

static void ApplyDebugSettings(PS4Emulator *emulator) {
  if (emulator) {
    spdlog::info("Applied debug settings: debug_mode={}, log_syscalls={}, "
                 "log_gpu_commands={}, log_level={}",
                 settings.debug_mode, settings.log_syscalls,
                 settings.log_gpu_commands, settings.log_level);
    spdlog::set_level(spdlog::level::from_str(settings.log_level));
  }
}

static void ApplyInputSettings(PS4Emulator *emulator) {
  if (emulator) {
    spdlog::info("Applied input settings: controller_enabled={}, "
                 "keyboard_enabled={}, mouse_enabled={}, deadzone={}",
                 settings.controller_enabled, settings.keyboard_enabled,
                 settings.mouse_enabled, settings.controller_deadzone);
    // InputManager would need methods to apply these settings
  }
}

static void ApplyNetworkSettings(PS4Emulator *emulator) {
  if (emulator) {
    spdlog::info("Applied network settings: enabled={}, interface={}, port={}",
                 settings.network_enabled, settings.network_interface,
                 settings.network_port);
    // Network manager would need methods to apply these settings
  }
}

static void ApplyCompatibilitySettings(PS4Emulator *emulator) {
  if (emulator) {
    spdlog::info("Applied compatibility settings: strict_mode={}, "
                 "ignore_missing_imports={}, patch_games={}",
                 settings.strict_mode, settings.ignore_missing_imports,
                 settings.patch_games);
    // Emulator core would need methods to apply these settings
  }
}

static void ApplyAdvancedSettings(PS4Emulator *emulator) {
  if (emulator) {
    spdlog::info("Applied advanced settings: emulation_speed={}, "
                 "auto_save_states={}, auto_save_interval={}",
                 settings.emulation_speed, settings.auto_save_states,
                 settings.auto_save_interval);
    // Emulator core would need methods to apply these settings
  }
}

// --- UI Helper Functions for Settings ---
static void HelpMarker(const char *desc) {
  ImGui::TextDisabled("(?)");
  if (ImGui::IsItemHovered(ImGuiHoveredFlags_DelayShort)) {
    ImGui::SetTooltip("%s", desc);
  }
}

template <typename T>
static bool DrawSettingSlider(const char *label, T *value, T min_value,
                              T max_value, const char *format,
                              const char *help_text = nullptr) {
  bool changed = false;
  if constexpr (std::is_same_v<T, int>) {
    changed = ImGui::SliderInt(label, value, min_value, max_value);
  } else if constexpr (std::is_same_v<T, float>) {
    changed = ImGui::SliderFloat(label, value, min_value, max_value, format);
  }
  if (help_text) {
    ImGui::SameLine();
    HelpMarker(help_text);
  }
  return changed;
}

static bool DrawSettingCheckbox(const char *label, bool *value,
                                const char *help_text = nullptr) {
  bool changed = ImGui::Checkbox(label, value);
  if (help_text) {
    ImGui::SameLine();
    HelpMarker(help_text);
  }
  return changed;
}

static bool DrawSettingCombo(const char *label, int *current_item,
                             const char *const items[], int items_count,
                             const char *help_text = nullptr) {
  bool changed = ImGui::Combo(label, current_item, items, items_count);
  if (help_text) {
    ImGui::SameLine();
    HelpMarker(help_text);
  }
  return changed;
}

static bool DrawSettingInputText(const char *label, char *buf, size_t buf_size,
                                 const char *help_text = nullptr) {
  bool changed = ImGui::InputText(label, buf, buf_size);
  if (help_text) {
    ImGui::SameLine();
    HelpMarker(help_text);
  }
  return changed;
}

void DrawDisplaySettingsUI(SDL_Window *window, PS4Emulator *emulator) {
  ImGui::Begin("Display Settings", &show_display_settings);
  ImGui::Text("Window Settings");
  ImGui::Separator();
  DrawSettingSlider("Window Width", &settings.width, 640, 3840, "%d");
  DrawSettingSlider("Window Height", &settings.height, 480, 2160, "%d");
  DrawSettingCheckbox("Fullscreen", &settings.fullscreen);
  DrawSettingCheckbox("VSync", &settings.vsync);
  DrawSettingSlider("FPS Limit", &settings.framerate_limit, 30, 240, "%d");
  DrawSettingSlider("UI Scale", &settings.ui_scale, 0.5f, 3.0f, "%.1f",
                    "Adjusts the size of UI elements and fonts.");

  ImGui::Spacing();
  ImGui::Text("Performance Overlay");
  ImGui::Separator();
  DrawSettingCheckbox("Show FPS", &settings.show_fps);
  DrawSettingCheckbox("Show Performance Metrics", &settings.show_performance,
                      "Displays detailed performance statistics.");

  ImGui::Spacing();
  ImGui::Text("Recent Games");
  ImGui::Separator();
  if (ImGui::Button("Clear Recent Games")) {
    settings.recent_games.clear();
    settings.last_game_path = "";
    SaveSettings();
  }
  ImGui::SameLine();
  HelpMarker("Removes all entries from the 'Recent Games' list.");

  ImGui::Spacing();
  if (ImGui::Button("Apply Display Settings")) {
    ApplyDisplaySettings(window);
    SaveSettings();
  }
  ImGui::SameLine();
  if (ImGui::Button("Save Settings"))
    SaveSettings();
  ImGui::End();
}

void DrawAudioSettingsUI(PS4Emulator *emulator) {
  ImGui::Begin("Audio Settings", &show_audio_settings);
  ImGui::Text("Audio Configuration");
  ImGui::Separator();

  // Add warning about audio initialization issues
  ImGui::TextColored(
      ImVec4(1.0f, 1.0f, 0.0f, 1.0f),
      "Note: If emulator hangs during startup, try disabling audio.");
  ImGui::TextColored(
      ImVec4(0.8f, 0.8f, 0.8f, 1.0f),
      "You can also set environment variable PS4EMU_DISABLE_AUDIO=1");
  ImGui::Separator();

  DrawSettingCheckbox("Audio Enabled", &settings.audio_enabled);
  DrawSettingSlider("Master Volume", &settings.master_volume, 0.0f, 1.0f,
                    "%.2f");
  DrawSettingSlider("SFX Volume", &settings.sfx_volume, 0.0f, 1.0f, "%.2f");
  DrawSettingSlider("Music Volume", &settings.music_volume, 0.0f, 1.0f, "%.2f");
  DrawSettingSlider("Audio Latency (ms)", &settings.audio_latency, 10, 200,
                    "%d",
                    "Lower latency might introduce crackling on some systems.");
  DrawSettingSlider("Underrun Warning Interval (s)",
                    &settings.audio_underrun_warning_interval, 0, 60, "%d",
                    "How often to log audio buffer underrun warnings. Set to 0 "
                    "to disable warnings.");

  // Dummy audio device selection
  const char *audio_devices[] = {"default", "HDMI", "Headphones"};
  int current_device_idx = 0;
  for (int i = 0; i < IM_ARRAYSIZE(audio_devices); ++i) {
    if (settings.audio_device == audio_devices[i]) {
      current_device_idx = i;
      break;
    }
  }
  if (DrawSettingCombo("Audio Device", &current_device_idx, audio_devices,
                       IM_ARRAYSIZE(audio_devices),
                       "Select the audio output device.")) {
    settings.audio_device = audio_devices[current_device_idx];
  }

  ImGui::Spacing();
  if (ImGui::Button("Apply Audio Settings")) {
    ApplyAudioSettings(emulator);
    SaveSettings();
  }
  ImGui::SameLine();
  if (ImGui::Button("Save Settings"))
    SaveSettings();
  ImGui::End();
}

void DrawCPUSettingsUI(PS4Emulator *emulator) {
  ImGui::Begin("CPU Settings", &show_cpu_settings);
  ImGui::Text("CPU Configuration");
  ImGui::Separator();
  DrawSettingSlider(
      "CPU Thread Count", &settings.cpu_thread_count, 1, 16, "%d",
      "Number of CPU threads dedicated to emulation. Requires restart.");
  DrawSettingCheckbox("JIT Compilation", &settings.jit_enabled,
                      "Enable Just-In-Time compilation for better performance. "
                      "Requires restart.");
  DrawSettingCheckbox("SIMD Optimizations", &settings.simd_optimizations,
                      "Enable Single Instruction, Multiple Data optimizations. "
                      "Requires restart.");
  DrawSettingCheckbox(
      "Branch Prediction", &settings.branch_prediction,
      "Enable CPU branch prediction optimizations. Requires restart.");
  DrawSettingSlider(
      "Cache Size (MB)", &settings.cache_size_mb, 64, 1024, "%d",
      "Size of the CPU instruction cache in MB. Requires restart.");

  ImGui::Spacing();
  ImGui::Text("Performance");
  ImGui::Separator();
  DrawSettingSlider("Emulation Speed", &settings.emulation_speed, 0.1f, 3.0f,
                    "%.1f",
                    "Adjusts the overall emulation speed (e.g., 0.5 for half "
                    "speed, 2.0 for double speed).");
  DrawSettingCheckbox(
      "Pause on Focus Loss", &settings.pause_on_focus_loss,
      "Automatically pauses emulation when the emulator window loses focus.");

  ImGui::Spacing();
  if (ImGui::Button("Apply CPU Settings")) {
    ApplyCPUSettings(emulator);
    SaveSettings();
  }
  ImGui::SameLine();
  if (ImGui::Button("Save Settings"))
    SaveSettings();
  ImGui::End();
}

void DrawGPUSettingsUI(PS4Emulator *emulator) {
  ImGui::Begin("GPU Settings", &show_gpu_settings);
  if (ImGui::BeginTabBar("GPUTabs")) {
    if (ImGui::BeginTabItem("Configuration")) {
      ImGui::Text("Graphics Configuration");
      ImGui::Separator();
      const char *backends[] = {
          "Vulkan", "OpenGL"}; // OpenGL is not fully implemented in this dummy
      int current_backend = (settings.gpu_backend == "Vulkan") ? 0 : 1;
      if (DrawSettingCombo(
              "GPU Backend", &current_backend, backends, 2,
              "Select the graphics API backend. Requires restart.")) {
        settings.gpu_backend = backends[current_backend];
      }
      DrawSettingSlider("Resolution Scale", &settings.resolution_scale, 1, 4,
                        "%d",
                        "Renders the game at a higher internal resolution "
                        "(e.g., 2x for 1440p on 720p base).");
      DrawSettingCheckbox("Anisotropic Filtering",
                          &settings.anisotropic_filtering,
                          "Improves texture quality at oblique angles.");
      if (settings.anisotropic_filtering) {
        DrawSettingSlider(
            "Anisotropy Level", &settings.anisotropy_level, 1, 16, "%d",
            "Higher values provide better quality but can impact performance.");
      }
      DrawSettingCheckbox("Anti-Aliasing", &settings.anti_aliasing,
                          "Reduces jagged edges on objects.");
      if (settings.anti_aliasing) {
        const char *aa_methods[] = {"FXAA", "MSAA 2x", "MSAA 4x", "MSAA 8x"};
        int current_aa = 0;
        if (settings.aa_method == "MSAA 2x")
          current_aa = 1;
        else if (settings.aa_method == "MSAA 4x")
          current_aa = 2;
        else if (settings.aa_method == "MSAA 8x")
          current_aa = 3;
        if (DrawSettingCombo("AA Method", &current_aa, aa_methods, 4,
                             "FXAA is a post-processing effect, MSAA is a "
                             "rendering technique. MSAA requires restart.")) {
          settings.aa_method = aa_methods[current_aa];
        }
      }
      ImGui::Spacing();
      ImGui::Text("Shader Settings");
      ImGui::Separator();
      DrawSettingCheckbox(
          "Shader Cache", &settings.shader_cache,
          "Caches compiled shaders to reduce stuttering on subsequent runs.");
      DrawSettingCheckbox("Async Shader Compilation", &settings.async_shaders,
                          "Compiles shaders in the background to prevent "
                          "freezing during gameplay.");
      ImGui::EndTabItem();
    }
    if (ImGui::BeginTabItem("Statistics")) {
      ImGui::Text("GPU Statistics");
      ImGui::Separator();
      if (emulator) {
        auto gpuStats = emulator->GetGPU().GetStats();
        ImGui::Text("Shader Count: %llu", gpuStats.shaderCount.load());
        ImGui::Text("Draw Count: %llu", gpuStats.drawCount.load());
        ImGui::Text("Cache Hits: %llu", gpuStats.cacheHits.load());
        ImGui::Text("Cache Misses: %llu", gpuStats.cacheMisses.load());
        ImGui::Text("Total Latency: %llu us", gpuStats.totalLatencyUs.load());
        ImGui::Text("Errors: %llu", gpuStats.errorCount.load());
      } else {
        ImGui::Text("Emulator not initialized.");
      }
      ImGui::Spacing();
      ImGui::Text("GPU Actions");
      ImGui::Separator();
      if (ImGui::Button("Clear Shader Cache")) {
        if (emulator)
          emulator->GetGPU().ClearShaderCache();
        spdlog::info("Shader cache cleared.");
      }
      ImGui::SameLine();
      HelpMarker(
          "Deletes all cached shaders. May cause stuttering on next run.");
      if (ImGui::Button("Clear Render Target Cache")) {
        if (emulator)
          emulator->GetGPU().ClearRenderTargetCache();
        spdlog::info("Render target cache cleared.");
      }
      ImGui::SameLine();
      HelpMarker("Clears the render target cache. Useful for resolving "
                 "rendering glitches.");
      ImGui::EndTabItem();
    }
    ImGui::EndTabBar();
  }
  ImGui::Spacing();
  if (ImGui::Button("Apply GPU Settings")) {
    ApplyGPUSettings(emulator);
    SaveSettings();
  }
  ImGui::SameLine();
  if (ImGui::Button("Save Settings"))
    SaveSettings();
  ImGui::End();
}

void DrawMemorySettingsUI(PS4Emulator *emulator) {
  ImGui::Begin("Memory Settings", &show_memory_settings);
  ImGui::Text("Memory Configuration");
  ImGui::Separator();
  DrawSettingSlider(
      "Memory Size (GB)", &settings.memory_size_gb, 1, 32, "%d",
      "Amount of RAM allocated to the emulated PS4. Requires restart.");
  DrawSettingCheckbox("Memory Compression", &settings.memory_compression,
                      "Compresses emulated memory to reduce host RAM usage. "
                      "Can impact performance. Requires restart.");
  DrawSettingSlider("Swap Size (GB)", &settings.swap_size_gb, 0, 16, "%d",
                    "Amount of disk space used as swap memory for the "
                    "emulator. Requires restart.");

  ImGui::Spacing();
  if (ImGui::Button("Apply Memory Settings")) {
    ApplyMemorySettings(emulator);
    SaveSettings();
  }
  ImGui::SameLine();
  if (ImGui::Button("Save Settings"))
    SaveSettings();
  ImGui::End();
}

void DrawFilesystemSettingsUI(PS4Emulator *emulator) {
  ImGui::Begin("Filesystem Settings", &show_filesystem_settings);
  if (emulator) {
    auto &fs = emulator->GetFilesystem();
    auto fsSettings = fs.GetSettings(); // Get a copy to modify

    ImGui::Text("Directory Paths");
    ImGui::Separator();
    static char gameDirBuf[260];
    std::strncpy(gameDirBuf, settings.game_directory.c_str(),
                 sizeof(gameDirBuf));
    gameDirBuf[sizeof(gameDirBuf) - 1] = '\0';
    DrawSettingInputText("Game Directory", gameDirBuf, sizeof(gameDirBuf));
    ImGui::SameLine();
    if (ImGui::Button("Browse##GameDir")) {
      std::string path = OpenFolderDialog("Select Game Directory");
      if (!path.empty()) {
        settings.game_directory = path;
        std::strncpy(gameDirBuf, path.c_str(), sizeof(gameDirBuf));
        gameDirBuf[sizeof(gameDirBuf) - 1] = '\0';
      }
    }
    ImGui::SameLine();
    HelpMarker("Directory where your PS4 game files are located.");

    static char saveDirBuf[260];
    std::strncpy(saveDirBuf, settings.save_directory.c_str(),
                 sizeof(saveDirBuf));
    saveDirBuf[sizeof(saveDirBuf) - 1] = '\0';
    DrawSettingInputText("Save Data Directory", saveDirBuf, sizeof(saveDirBuf));
    ImGui::SameLine();
    if (ImGui::Button("Browse##SaveDir")) {
      std::string path = OpenFolderDialog("Select Save Data Directory");
      if (!path.empty()) {
        settings.save_directory = path;
        std::strncpy(saveDirBuf, path.c_str(), sizeof(saveDirBuf));
        saveDirBuf[sizeof(saveDirBuf) - 1] = '\0';
      }
    }
    ImGui::SameLine();
    HelpMarker("Directory for emulated game save files.");

    static char stateDirBuf[260];
    std::strncpy(stateDirBuf, settings.state_directory.c_str(),
                 sizeof(stateDirBuf));
    stateDirBuf[sizeof(stateDirBuf) - 1] = '\0';
    DrawSettingInputText("Save State Directory", stateDirBuf,
                         sizeof(stateDirBuf));
    ImGui::SameLine();
    if (ImGui::Button("Browse##StateDir")) {
      std::string path = OpenFolderDialog("Select Save State Directory");
      if (!path.empty()) {
        settings.state_directory = path;
        std::strncpy(stateDirBuf, path.c_str(), sizeof(stateDirBuf));
        stateDirBuf[sizeof(stateDirBuf) - 1] = '\0';
      }
    }
    ImGui::SameLine();
    HelpMarker("Directory for emulator save states.");

    static char screenshotDirBuf[260];
    std::strncpy(screenshotDirBuf, settings.screenshot_directory.c_str(),
                 sizeof(screenshotDirBuf));
    screenshotDirBuf[sizeof(screenshotDirBuf) - 1] = '\0';
    DrawSettingInputText("Screenshot Directory", screenshotDirBuf,
                         sizeof(screenshotDirBuf));
    ImGui::SameLine();
    if (ImGui::Button("Browse##ScreenshotDir")) {
      std::string path = OpenFolderDialog("Select Screenshot Directory");
      if (!path.empty()) {
        settings.screenshot_directory = path;
        std::strncpy(screenshotDirBuf, path.c_str(), sizeof(screenshotDirBuf));
        screenshotDirBuf[sizeof(screenshotDirBuf) - 1] = '\0';
      }
    }
    ImGui::SameLine();
    HelpMarker("Directory where screenshots will be saved.");

    static char dumpDirBuf[260];
    std::strncpy(dumpDirBuf, settings.dump_directory.c_str(),
                 sizeof(dumpDirBuf));
    dumpDirBuf[sizeof(dumpDirBuf) - 1] = '\0';
    DrawSettingInputText("Dump Directory", dumpDirBuf, sizeof(dumpDirBuf));
    ImGui::SameLine();
    if (ImGui::Button("Browse##DumpDir")) {
      std::string path = OpenFolderDialog("Select Dump Directory");
      if (!path.empty()) {
        settings.dump_directory = path;
        std::strncpy(dumpDirBuf, path.c_str(), sizeof(dumpDirBuf));
        dumpDirBuf[sizeof(dumpDirBuf) - 1] = '\0';
      }
    }
    ImGui::SameLine();
    HelpMarker(
        "Directory for various emulator dumps (e.g., memory, GPU traces).");

    ImGui::Spacing();
    ImGui::Text("Behavior");
    ImGui::Separator();
    DrawSettingCheckbox(
        "Auto Mount Games", &settings.auto_mount_games,
        "Automatically mounts games found in the game directory.");

    ImGui::Spacing();
    ImGui::Text("Advanced Filesystem Options");
    ImGui::Separator();
    static char mountPointBuf[128];
    std::strncpy(mountPointBuf, fsSettings.defaultMountPoint.c_str(),
                 sizeof(mountPointBuf));
    mountPointBuf[sizeof(mountPointBuf) - 1] = '\0';
    if (DrawSettingInputText(
            "Default Mount Point", mountPointBuf, sizeof(mountPointBuf),
            "Default virtual mount point for games (e.g., /app0).")) {
      fsSettings.defaultMountPoint = mountPointBuf;
    }

    int fileMode = static_cast<int>(fsSettings.defaultFileMode);
    if (ImGui::InputInt("Default File Mode (Octal)", &fileMode, 1, 100)) {
      fsSettings.defaultFileMode = static_cast<uint32_t>(fileMode);
    }
    ImGui::SameLine();
    HelpMarker("Default file permissions (octal) for new files in the emulated "
               "filesystem.");

    int dirMode = static_cast<int>(fsSettings.defaultDirMode);
    if (ImGui::InputInt("Default Dir Mode (Octal)", &dirMode, 1, 100)) {
      fsSettings.defaultDirMode = static_cast<uint32_t>(dirMode);
    }
    ImGui::SameLine();
    HelpMarker("Default directory permissions (octal) for new directories in "
               "the emulated filesystem.");

    DrawSettingCheckbox("Case Sensitive Paths",
                        &fsSettings.enableCaseSensitivity,
                        "Enforce case sensitivity for file paths within the "
                        "emulated filesystem.");

    ImGui::Spacing();
    ImGui::Text("Additional Mounts (Requires Restart)");
    ImGui::Separator();
    static char newMountPath[260] = "";
    ImGui::InputText("Host Path", newMountPath, sizeof(newMountPath));
    ImGui::SameLine();
    if (ImGui::Button("Browse##NewMount")) {
      std::string path = OpenFolderDialog("Select Folder to Mount");
      if (!path.empty()) {
        std::strncpy(newMountPath, path.c_str(), sizeof(newMountPath));
        newMountPath[sizeof(newMountPath) - 1] = '\0';
      }
    }
    ImGui::SameLine();
    if (ImGui::Button("Add Mount")) {
      if (std::filesystem::exists(newMountPath) &&
          std::filesystem::is_directory(newMountPath)) {
        fsSettings.additionalMounts.push_back(newMountPath);
        newMountPath[0] = '\0';
        spdlog::info("Added new mount: {}", newMountPath);
      } else {
        spdlog::error("Invalid path for new mount: {}", newMountPath);
        globalErrorMessage =
            fmt::format("Invalid path for new mount: {}", newMountPath);
      }
    }

    for (size_t i = 0; i < fsSettings.additionalMounts.size(); ++i) {
      ImGui::Text("Mount %zu: %s", i, fsSettings.additionalMounts[i].c_str());
      ImGui::SameLine();
      ImGui::PushID(i);
      if (ImGui::SmallButton("Remove")) {
        fsSettings.additionalMounts.erase(fsSettings.additionalMounts.begin() +
                                          i);
        i--; // Adjust index after removal
      }
      ImGui::PopID();
    }

    ImGui::Spacing();
    if (ImGui::Button("Apply Filesystem Settings")) {
      settings.game_directory = gameDirBuf;
      settings.save_directory = saveDirBuf;
      settings.state_directory = stateDirBuf;
      settings.screenshot_directory = screenshotDirBuf;
      settings.dump_directory = dumpDirBuf;
      fs.SetSettings(fsSettings); // Apply the modified fsSettings
      ApplyFilesystemSettings(emulator);
      SaveSettings();
    }
    ImGui::SameLine();
    if (ImGui::Button("Save Settings"))
      SaveSettings();
  } else {
    ImGui::Text("Emulator not initialized.");
  }
  ImGui::End();
}

void DrawDebugSettingsUI(PS4Emulator *emulator) {
  ImGui::Begin("Debug Settings", &show_debug_settings);
  ImGui::Text("Debugging Options");
  ImGui::Separator();
  DrawSettingCheckbox("Debug Mode", &settings.debug_mode,
                      "Enables additional debugging features and logging.");
  DrawSettingCheckbox("Log Syscalls", &settings.log_syscalls,
                      "Logs all system calls made by the emulated system.");
  DrawSettingCheckbox("Log GPU Commands", &settings.log_gpu_commands,
                      "Logs all GPU commands sent to the emulated GPU.");

  const char *log_levels[] = {"trace", "debug",    "info", "warn",
                              "error", "critical", "off"};
  int current_log_level_idx = 0;
  for (int i = 0; i < IM_ARRAYSIZE(log_levels); ++i) {
    if (settings.log_level == log_levels[i]) {
      current_log_level_idx = i;
      break;
    }
  }
  if (DrawSettingCombo(
          "Log Level", &current_log_level_idx, log_levels,
          IM_ARRAYSIZE(log_levels),
          "Sets the verbosity of the emulator's logging output.")) {
    settings.log_level = log_levels[current_log_level_idx];
  }

  ImGui::Spacing();
  if (ImGui::Button("Apply Debug Settings")) {
    ApplyDebugSettings(emulator);
    SaveSettings();
  }
  ImGui::SameLine();
  if (ImGui::Button("Save Settings"))
    SaveSettings();
  ImGui::End();
}

void DrawInputSettingsUI(PS4Emulator *emulator) {
  ImGui::Begin("Input Settings", &show_input_settings);
  ImGui::Text("Input Device Configuration");
  ImGui::Separator();
  DrawSettingCheckbox("Controller Enabled", &settings.controller_enabled,
                      "Enable or disable controller input.");
  DrawSettingSlider("Controller Deadzone", &settings.controller_deadzone, 0.0f,
                    0.5f, "%.2f",
                    "Adjusts the deadzone for analog stick input.");
  DrawSettingCheckbox("Keyboard Enabled", &settings.keyboard_enabled,
                      "Enable or disable keyboard input.");
  DrawSettingCheckbox("Mouse Enabled", &settings.mouse_enabled,
                      "Enable or disable mouse input.");

  ImGui::Spacing();
  if (ImGui::Button("Apply Input Settings")) {
    ApplyInputSettings(emulator);
    SaveSettings();
  }
  ImGui::SameLine();
  if (ImGui::Button("Save Settings"))
    SaveSettings();

  if (inputSettings) {
    ImGui::Separator();
    inputSettings->Draw(
        &show_input_settings); // Draw the specific input settings UI
  }
  ImGui::End();
}

void DrawNetworkSettingsUI(PS4Emulator *emulator) {
  ImGui::Begin("Network Settings", &show_network_settings);
  ImGui::Text("Network Configuration");
  ImGui::Separator();
  DrawSettingCheckbox(
      "Network Enabled", &settings.network_enabled,
      "Enable or disable network connectivity for the emulated system.");

  static char networkInterfaceBuf[64];
  std::strncpy(networkInterfaceBuf, settings.network_interface.c_str(),
               sizeof(networkInterfaceBuf));
  networkInterfaceBuf[sizeof(networkInterfaceBuf) - 1] = '\0';
  if (DrawSettingInputText("Network Interface", networkInterfaceBuf,
                           sizeof(networkInterfaceBuf),
                           "Specify a network interface (e.g., 'eth0', "
                           "'Wi-Fi'). 'auto' for automatic detection.")) {
    settings.network_interface = networkInterfaceBuf;
  }

  DrawSettingSlider("Network Port", &settings.network_port, 1, 65535, "%d",
                    "Port used for network communication (e.g., for remote "
                    "debugging or multiplayer).");

  ImGui::Spacing();
  if (ImGui::Button("Apply Network Settings")) {
    ApplyNetworkSettings(emulator);
    SaveSettings();
  }
  ImGui::SameLine();
  if (ImGui::Button("Save Settings"))
    SaveSettings();
  ImGui::End();
}

void DrawCompatibilitySettingsUI(PS4Emulator *emulator) {
  ImGui::Begin("Compatibility Settings", &show_compatibility_settings);
  ImGui::Text("Game Compatibility Options");
  ImGui::Separator();
  DrawSettingCheckbox("Strict Mode", &settings.strict_mode,
                      "Enforces stricter emulation accuracy, potentially "
                      "reducing performance but improving compatibility.");
  DrawSettingCheckbox("Ignore Missing Imports",
                      &settings.ignore_missing_imports,
                      "Attempts to run games even if some required library "
                      "imports are missing. Can cause instability.");
  DrawSettingCheckbox("Patch Games", &settings.patch_games,
                      "Applies community-made patches to improve game "
                      "compatibility or performance.");

  ImGui::Spacing();
  if (ImGui::Button("Apply Compatibility Settings")) {
    ApplyCompatibilitySettings(emulator);
    SaveSettings();
  }
  ImGui::SameLine();
  if (ImGui::Button("Save Settings"))
    SaveSettings();
  ImGui::End();
}

void DrawAdvancedSettingsUI(PS4Emulator *emulator) {
  ImGui::Begin("Advanced Settings", &show_advanced_settings);
  ImGui::Text("Miscellaneous Advanced Options");
  ImGui::Separator();
  DrawSettingCheckbox(
      "Auto Save States", &settings.auto_save_states,
      "Automatically saves the emulator state at regular intervals.");
  if (settings.auto_save_states) {
    DrawSettingSlider("Auto Save Interval (seconds)",
                      &settings.auto_save_interval, 60, 3600, "%d",
                      "Frequency of automatic save state creation.");
  }

  ImGui::Spacing();
  ImGui::Text("Cache Statistics");
  ImGui::Separator();
  ImGui::Text("Settings Cache Hits: %llu", settings.cacheHits);
  ImGui::Text("Settings Cache Misses: %llu", settings.cacheMisses);
  ImGui::SameLine();
  HelpMarker("Statistics for loading/saving emulator settings.");

  ImGui::Spacing();
  if (ImGui::Button("Apply Advanced Settings")) {
    ApplyAdvancedSettings(emulator);
    SaveSettings();
  }
  ImGui::SameLine();
  if (ImGui::Button("Save Settings"))
    SaveSettings();
  ImGui::SameLine();
  if (ImGui::Button("Reset Settings to Default")) {
    settings = EmulatorSettings(); // Reset to default constructor values
    SaveSettings();
    // Re-apply all settings that can be applied immediately
    ApplyDisplaySettings(sdl_window_ptr);
    ApplyAudioSettings(emulator);
    ApplyCPUSettings(emulator);    // Will log warning about restart
    ApplyGPUSettings(emulator);    // Will log warning about restart
    ApplyMemorySettings(emulator); // Will log warning about restart
    ApplyFilesystemSettings(emulator);
    ApplyDebugSettings(emulator);
    ApplyInputSettings(emulator);
    ApplyNetworkSettings(emulator);
    ApplyCompatibilitySettings(emulator);
    ApplyAdvancedSettings(emulator);
    spdlog::info("All settings reset to default values.");
    globalErrorMessage =
        "All settings reset to default values. Some changes require restart.";
  }
  ImGui::SameLine();
  HelpMarker("Resets all emulator settings to their default values. Requires "
             "restart for some changes.");
  ImGui::End();
}

int main() {
#ifdef _WIN32
  EnablePerMonitorV2DpiAwareness();
#endif

  auto file_sink =
      std::make_shared<spdlog::sinks::basic_file_sink_mt>("emulator.log", true);
  spdlog::default_logger()->sinks().push_back(file_sink);
  spdlog::set_level(spdlog::level::debug);
  spdlog::set_pattern("[%Y-%m-%d %H:%M:%S.%e] [%l] %v");
  spdlog::info("Starting PS4 Emulator");
  LoadSettings();
  std::unique_ptr<PS4Emulator>
      emulator; // Consider using an interface here for extensibility
  VulkanContext
      vk_context; // Consider abstracting this behind a rendering interface

  try {
    if (SDL_Init(SDL_INIT_VIDEO | SDL_INIT_EVENTS | SDL_INIT_GAMECONTROLLER) !=
        0) {
      spdlog::error("SDL_Init failed: {}", SDL_GetError());
      globalErrorMessage = fmt::format("SDL_Init failed: {}", SDL_GetError());
      return EXIT_FAILURE;
    }

#ifdef SDL_HINT_IME_SHOW_UI
    SDL_SetHint(SDL_HINT_IME_SHOW_UI, "1");
#endif

    std::unique_ptr<SDL_Window, void (*)(SDL_Window *)> window(
        SDL_CreateWindow(kWindowTitle, SDL_WINDOWPOS_CENTERED,
                         SDL_WINDOWPOS_CENTERED, kDefaultWidth, kDefaultHeight,
                         SDL_WINDOW_SHOWN | SDL_WINDOW_RESIZABLE |
                             SDL_WINDOW_VULKAN | SDL_WINDOW_ALLOW_HIGHDPI),
        [](SDL_Window *w) {
          if (w)
            SDL_DestroyWindow(w);
        });
    if (!window) {
      spdlog::error("SDL_CreateWindow failed: {}", SDL_GetError());
      globalErrorMessage =
          fmt::format("SDL_CreateWindow failed: {}", SDL_GetError());
      return EXIT_FAILURE;
    }
    sdl_window_ptr =
        window.get(); // Store the raw pointer for use in settings functions
    {
      std::shared_lock<std::shared_mutex> lock(settingsMutex);
      if (settings.fullscreen) {
        SDL_SetWindowFullscreen(window.get(), SDL_WINDOW_FULLSCREEN_DESKTOP);
      }
      SDL_SetWindowSize(window.get(), settings.width, settings.height);
      std::strncpy(gamePathBuf, settings.last_game_path.c_str(),
                   sizeof(gamePathBuf));
    }

    unsigned num_extensions;
    if (!SDL_Vulkan_GetInstanceExtensions(nullptr, &num_extensions, nullptr)) {
      spdlog::error("Vulkan not supported by SDL: {}", SDL_GetError());
      globalErrorMessage = "Vulkan not supported by SDL.";
      throw std::runtime_error("Vulkan not supported");
    }
    std::vector<const char *> extensions(num_extensions);
    SDL_Vulkan_GetInstanceExtensions(window.get(), &num_extensions,
                                     extensions.data());
    extensions.push_back(VK_EXT_DEBUG_UTILS_EXTENSION_NAME);

    VkApplicationInfo app_info = {.sType = VK_STRUCTURE_TYPE_APPLICATION_INFO,
                                  .pApplicationName = "PS4 Emulator",
                                  .applicationVersion =
                                      VK_MAKE_VERSION(1, 0, 0),
                                  .pEngineName = "Custom",
                                  .engineVersion = VK_MAKE_VERSION(1, 0, 0),
                                  .apiVersion = VK_API_VERSION_1_0};

    VkInstanceCreateInfo create_info = {
        .sType = VK_STRUCTURE_TYPE_INSTANCE_CREATE_INFO,
        .pApplicationInfo = &app_info,
        .enabledLayerCount = 0,
        .enabledExtensionCount = static_cast<uint32_t>(extensions.size()),
        .ppEnabledExtensionNames = extensions.data()};

    if (vkCreateInstance(&create_info, nullptr, &vk_context.instance) !=
        VK_SUCCESS) {
      spdlog::error("Failed to create Vulkan instance");
      globalErrorMessage = "Failed to create Vulkan instance.";
      throw std::runtime_error("Vulkan instance creation failed");
    }

    if (!SDL_Vulkan_CreateSurface(window.get(), vk_context.instance,
                                  &vk_context.surface)) {
      spdlog::error("Failed to create Vulkan surface: {}", SDL_GetError());
      globalErrorMessage =
          fmt::format("Failed to create Vulkan surface: {}", SDL_GetError());
      throw std::runtime_error("Vulkan surface creation failed");
    }

    uint32_t device_count = 0;
    if (vkEnumeratePhysicalDevices(vk_context.instance, &device_count,
                                   nullptr) != VK_SUCCESS ||
        device_count == 0) {
      spdlog::error("Failed to find GPUs with Vulkan support");
      globalErrorMessage = "Failed to find GPUs with Vulkan support.";
      throw std::runtime_error("No Vulkan-compatible GPUs found");
    }
    std::vector<VkPhysicalDevice> devices(device_count);
    vkEnumeratePhysicalDevices(vk_context.instance, &device_count,
                               devices.data());

    for (const auto &device : devices) {
      uint32_t queue_family_count = 0;
      vkGetPhysicalDeviceQueueFamilyProperties(device, &queue_family_count,
                                               nullptr);
      std::vector<VkQueueFamilyProperties> queue_families(queue_family_count);
      vkGetPhysicalDeviceQueueFamilyProperties(device, &queue_family_count,
                                               queue_families.data());
      for (uint32_t i = 0; i < queue_families.size(); i++) {
        VkBool32 present_support = false;
        vkGetPhysicalDeviceSurfaceSupportKHR(device, i, vk_context.surface,
                                             &present_support);
        if (queue_families[i].queueFlags & VK_QUEUE_GRAPHICS_BIT &&
            present_support) {
          vk_context.physicalDevice = device;
          vk_context.graphicsQueueFamily = i;
          vk_context.presentQueueFamily = i;
          break;
        }
      }
      if (vk_context.physicalDevice)
        break;
    }
    if (!vk_context.physicalDevice) {
      spdlog::error("Failed to find a suitable GPU");
      globalErrorMessage = "Failed to find a suitable GPU.";
      throw std::runtime_error("No suitable GPU found");
    }

    float queue_priority = 1.0f;
    VkDeviceQueueCreateInfo queue_info = {
        .sType = VK_STRUCTURE_TYPE_DEVICE_QUEUE_CREATE_INFO,
        .queueFamilyIndex = vk_context.graphicsQueueFamily,
        .queueCount = 1,
        .pQueuePriorities = &queue_priority};

    VkPhysicalDeviceFeatures device_features = {};
    std::vector<const char *> device_extensions = {
        VK_KHR_SWAPCHAIN_EXTENSION_NAME};

    VkDeviceCreateInfo device_create_info = {
        .sType = VK_STRUCTURE_TYPE_DEVICE_CREATE_INFO,
        .queueCreateInfoCount = 1,
        .pQueueCreateInfos = &queue_info,
        .enabledExtensionCount =
            static_cast<uint32_t>(device_extensions.size()),
        .ppEnabledExtensionNames = device_extensions.data(),
        .pEnabledFeatures = &device_features};

    if (vkCreateDevice(vk_context.physicalDevice, &device_create_info, nullptr,
                       &vk_context.device) != VK_SUCCESS) {
      spdlog::error("Failed to create logical device");
      globalErrorMessage = "Failed to create logical device.";
      throw std::runtime_error("Vulkan device creation failed");
    }

    vkGetDeviceQueue(vk_context.device, vk_context.graphicsQueueFamily, 0,
                     &vk_context.graphicsQueue);
    vkGetDeviceQueue(vk_context.device, vk_context.presentQueueFamily, 0,
                     &vk_context.presentQueue);

    VkCommandPoolCreateInfo pool_info = {
        .sType = VK_STRUCTURE_TYPE_COMMAND_POOL_CREATE_INFO,
        .flags = VK_COMMAND_POOL_CREATE_RESET_COMMAND_BUFFER_BIT,
        .queueFamilyIndex = vk_context.graphicsQueueFamily};

    if (vkCreateCommandPool(vk_context.device, &pool_info, nullptr,
                            &vk_context.commandPool) != VK_SUCCESS) {
      spdlog::error("Failed to create command pool");
      globalErrorMessage = "Failed to create command pool.";
      throw std::runtime_error("Vulkan command pool creation failed");
    }

    createSwapchain(vk_context, window.get());
    createRenderPass(vk_context);
    createFramebuffers(vk_context);
    createDescriptorPool(vk_context);

    VkSemaphoreCreateInfo semaphoreInfo = {
        VK_STRUCTURE_TYPE_SEMAPHORE_CREATE_INFO};
    if (vkCreateSemaphore(vk_context.device, &semaphoreInfo, nullptr,
                          &vk_context.imageAvailableSemaphore) != VK_SUCCESS ||
        vkCreateSemaphore(vk_context.device, &semaphoreInfo, nullptr,
                          &vk_context.renderFinishedSemaphore) != VK_SUCCESS) {
      globalErrorMessage = "Failed to create semaphores.";
      throw std::runtime_error("Failed to create semaphores");
    }

    VkFenceCreateInfo fenceInfo = {VK_STRUCTURE_TYPE_FENCE_CREATE_INFO, nullptr,
                                   VK_FENCE_CREATE_SIGNALED_BIT};
    if (vkCreateFence(vk_context.device, &fenceInfo, nullptr,
                      &vk_context.inFlightFence) != VK_SUCCESS) {
      globalErrorMessage = "Failed to create fence.";
      throw std::runtime_error("Failed to create fence");
    }

    // Ensure Vulkan context is fully initialized before emulator starts
    spdlog::info(
        "Vulkan context initialization complete, waiting for device idle...");
    vkDeviceWaitIdle(vk_context.device);
    spdlog::info("Vulkan device idle, starting emulator initialization");

    // Extensibility: PS4Emulator could be an interface (e.g., IEmulator)
    // and its concrete implementation injected here.
    emulator = std::make_unique<PS4Emulator>();

    auto emulatorInitFuture = std::async(std::launch::async, [&]() {
      spdlog::info("Initializing PS4 emulator asynchronously");
      try {
        // Small delay to ensure main thread Vulkan setup is complete
        std::this_thread::sleep_for(std::chrono::milliseconds(100));
        bool result = emulator->Initialize(window.get(), &vk_context);
        return result;
      } catch (const std::exception &e) {
        spdlog::error("Emulator initialization failed: {}", e.what());
        globalErrorMessage =
            fmt::format("Emulator initialization failed: {}", e.what());
        return false;
      }
    });

    bool emulatorInitDone = false;
    bool emulatorInitSuccess = false;
    std::unique_ptr<InputManager>
        inputManager; // Extensibility: InputManager could also be an interface
    std::future<bool> inputInitFuture;

    IMGUI_CHECKVERSION();
    ImGui::CreateContext();
    ImGuiIO &io = ImGui::GetIO();

    // Initial font setup (will be re-applied by ApplyDisplaySettings)
    io.Fonts->Clear();
    ImFont *font = io.Fonts->AddFontDefault();
    io.FontDefault = font;
    io.Fonts->Build();

    ImGui::StyleColorsDark();
    ImGuiStyle &style = ImGui::GetStyle();
    style.Colors[ImGuiCol_WindowBg] = ImVec4(0.12f, 0.12f, 0.12f, 1.00f);
    style.Colors[ImGuiCol_Header] = ImVec4(0.062f, 0.439f, 0.812f, 1.00f);
    style.Colors[ImGuiCol_HeaderHovered] =
        ImVec4(0.082f, 0.518f, 0.910f, 1.00f);
    style.Colors[ImGuiCol_HeaderActive] = ImVec4(0.082f, 0.518f, 0.910f, 1.00f);
    style.Colors[ImGuiCol_Button] = ImVec4(0.15f, 0.15f, 0.15f, 1.00f);
    style.Colors[ImGuiCol_ButtonHovered] =
        ImVec4(0.062f, 0.439f, 0.812f, 1.00f);
    style.Colors[ImGuiCol_ButtonActive] = ImVec4(0.082f, 0.518f, 0.910f, 1.00f);
    style.Colors[ImGuiCol_FrameBg] = ImVec4(0.10f, 0.10f, 0.10f, 1.00f);
    style.Colors[ImGuiCol_FrameBgHovered] = ImVec4(0.15f, 0.15f, 0.20f, 1.00f);
    style.Colors[ImGuiCol_FrameBgActive] = ImVec4(0.15f, 0.15f, 0.20f, 1.00f);
    style.Colors[ImGuiCol_TitleBg] = ImVec4(0.08f, 0.08f, 0.10f, 1.00f);
    style.Colors[ImGuiCol_TitleBgActive] = ImVec4(0.10f, 0.10f, 0.12f, 1.00f);
    style.WindowRounding = 4.0f;
    style.FrameRounding = 3.0f;
    style.GrabRounding = 3.0f;
    style.ScrollbarSize = 14.0f;
    style.FrameBorderSize = 1.0f;

    // Apply initial display settings including font scaling
    ApplyDisplaySettings(window.get());

    if (!ImGui_ImplSDL2_InitForVulkan(window.get())) {
      spdlog::error("ImGui_ImplSDL2_InitForVulkan failed");
      globalErrorMessage = "ImGui_ImplSDL2_InitForVulkan failed.";
      throw std::runtime_error("ImGui SDL2 initialization failed");
    }

    ImGui_ImplVulkan_InitInfo vk_init_info = {};
    vk_init_info.Instance = vk_context.instance;
    vk_init_info.PhysicalDevice = vk_context.physicalDevice;
    vk_init_info.Device = vk_context.device;
    vk_init_info.QueueFamily = vk_context.graphicsQueueFamily;
    vk_init_info.Queue = vk_context.graphicsQueue;
    vk_init_info.DescriptorPool = vk_context.descriptorPool;
    vk_init_info.MinImageCount =
        static_cast<uint32_t>(vk_context.swapchainImages.size());
    vk_init_info.ImageCount =
        static_cast<uint32_t>(vk_context.swapchainImages.size());
    vk_init_info.MSAASamples = VK_SAMPLE_COUNT_1_BIT;
    vk_init_info.CheckVkResultFn = check_vk_result;
    vk_init_info.RenderPass = vk_context.renderPass;

    if (!ImGui_ImplVulkan_Init(&vk_init_info)) {
      spdlog::error("ImGui_ImplVulkan_Init failed");
      globalErrorMessage = "ImGui_ImplVulkan_Init failed.";
      throw std::runtime_error("ImGui Vulkan initialization failed");
    }

    VkCommandBufferAllocateInfo alloc_info = {
        .sType = VK_STRUCTURE_TYPE_COMMAND_BUFFER_ALLOCATE_INFO,
        .commandPool = vk_context.commandPool,
        .level = VK_COMMAND_BUFFER_LEVEL_PRIMARY,
        .commandBufferCount = 1};

    VkCommandBuffer command_buffer;
    if (vkAllocateCommandBuffers(vk_context.device, &alloc_info,
                                 &command_buffer) != VK_SUCCESS) {
      spdlog::error("Failed to allocate command buffer for font upload");
      globalErrorMessage = "Failed to allocate command buffer for font upload.";
      throw std::runtime_error("Vulkan command buffer allocation failed");
    }

    VkCommandBufferBeginInfo begin_info = {
        .sType = VK_STRUCTURE_TYPE_COMMAND_BUFFER_BEGIN_INFO,
        .flags = VK_COMMAND_BUFFER_USAGE_ONE_TIME_SUBMIT_BIT};

    if (vkBeginCommandBuffer(command_buffer, &begin_info) != VK_SUCCESS ||
        !ImGui_ImplVulkan_CreateFontsTexture() ||
        vkEndCommandBuffer(command_buffer) != VK_SUCCESS) {
      spdlog::error("Failed to upload ImGui fonts");
      globalErrorMessage = "Failed to upload ImGui fonts.";
      throw std::runtime_error("ImGui font upload failed");
    }

    VkSubmitInfo submit_info = {.sType = VK_STRUCTURE_TYPE_SUBMIT_INFO,
                                .commandBufferCount = 1,
                                .pCommandBuffers = &command_buffer};

    if (vkQueueSubmit(vk_context.graphicsQueue, 1, &submit_info,
                      VK_NULL_HANDLE) != VK_SUCCESS) {
      spdlog::error("Failed to submit font upload command buffer");
      globalErrorMessage = "Failed to submit font upload command buffer.";
      throw std::runtime_error("Vulkan font upload submission failed");
    }

    vkDeviceWaitIdle(vk_context.device);
    ImGui_ImplVulkan_DestroyFontsTexture();
    vkFreeCommandBuffers(vk_context.device, vk_context.commandPool, 1,
                         &command_buffer);

    VkCommandBuffer render_command_buffer;
    if (vkAllocateCommandBuffers(vk_context.device, &alloc_info,
                                 &render_command_buffer) != VK_SUCCESS) {
      spdlog::error("Failed to allocate render command buffer");
      globalErrorMessage = "Failed to allocate render command buffer.";
      throw std::runtime_error(
          "Vulkan render command buffer allocation failed");
    }

    bool quit = false;
    SDL_Event e;
    auto lastFrame = std::chrono::steady_clock::now();
    const auto frameTime = std::chrono::milliseconds(16);
    bool swapchain_rebuild = false;
    ImVec4 clear_color = ImVec4(0.12f, 0.12f, 0.12f, 1.00f);
    bool show_demo_window = false;
    bool show_another_window = false;

    // Track initialization timeout
    auto initStartTime = std::chrono::steady_clock::now();
    const auto maxInitTime = std::chrono::seconds(
        120); // 120 second timeout (increased from 30 for slow disk I/O)

    while (!quit) {
      if (!emulatorInitDone) {
        // Check for initialization timeout with progress reporting
        auto currentTime = std::chrono::steady_clock::now();
        auto elapsedTime = std::chrono::duration_cast<std::chrono::seconds>(
            currentTime - initStartTime);

        // Report progress every 15 seconds
        static auto lastProgressReport = initStartTime;
        if (currentTime - lastProgressReport > std::chrono::seconds(15)) {
          spdlog::info("Emulator initialization in progress... {}s elapsed",
                       elapsedTime.count());
          lastProgressReport = currentTime;
        }

        if (currentTime - initStartTime > maxInitTime) {
          spdlog::error("Emulator initialization timed out after 120 seconds");
          globalErrorMessage = "Emulator initialization timed out. Check logs "
                               "for details on which component failed.";
          emulatorInitDone = true;
          emulatorInitSuccess = false;
        }

        if (emulatorInitFuture.wait_for(std::chrono::milliseconds(0)) ==
            std::future_status::ready) {
          emulatorInitSuccess = emulatorInitFuture.get();
          emulatorInitDone = true;
          if (emulatorInitSuccess) {
            spdlog::info("PS4 emulator initialized successfully");
            inputManager = std::make_unique<InputManager>(
                emulator->GetControllerManager());
            inputInitFuture = std::async(std::launch::async, [&]() {
              return inputManager->Initialize();
            });

            gameBrowser = new ps4::GameBrowser(emulator.get());
            inputSettings = new ps4::InputSettings(emulator.get());
            performanceOverlay = new ps4::PerformanceOverlay(emulator.get());

            // Apply initial settings to emulator components after
            // initialization
            ApplyAudioSettings(emulator.get());
            ApplyCPUSettings(emulator.get());
            ApplyGPUSettings(emulator.get());
            ApplyMemorySettings(emulator.get());
            ApplyFilesystemSettings(emulator.get());
            ApplyDebugSettings(emulator.get());
            ApplyInputSettings(emulator.get());
            ApplyNetworkSettings(emulator.get());
            ApplyCompatibilitySettings(emulator.get());
            ApplyAdvancedSettings(emulator.get());

            // Start the controller's input thread now that everything else is
            // ready
            emulator->GetControllerManager().StartInputThread();

          } else {
            spdlog::error("Emulator initialization failed");
            if (globalErrorMessage.empty()) {
              globalErrorMessage =
                  "Emulator initialization failed. Check logs for details.";
            }
            // Don't quit immediately - allow user to see error and try again
            // quit = true;
          }
        }
      }

      while (SDL_PollEvent(&e)) {
        ImGui_ImplSDL2_ProcessEvent(&e);
        if (e.type == SDL_QUIT)
          quit = true;
        if (e.type == SDL_WINDOWEVENT &&
            e.window.event == SDL_WINDOWEVENT_CLOSE &&
            e.window.windowID == SDL_GetWindowID(window.get()))
          quit = true;
      }

      if (swapchain_rebuild) {
        int width, height;
        SDL_Vulkan_GetDrawableSize(window.get(), &width, &height);
        if (width > 0 && height > 0) {
          cleanupSwapchain(vk_context);
          createSwapchain(vk_context, window.get());
          createFramebuffers(vk_context);
          swapchain_rebuild = false;
        }
      }

      ImGui_ImplVulkan_NewFrame();
      ImGui_ImplSDL2_NewFrame();
      ImGui::NewFrame();

      // Centralized Error Message Display
      if (!globalErrorMessage.empty()) {
        ImGui::OpenPopup("Error");
      }
      if (ImGui::BeginPopupModal("Error", nullptr,
                                 ImGuiWindowFlags_AlwaysAutoResize)) {
        ImGui::TextWrapped("%s", globalErrorMessage.c_str());
        if (ImGui::Button("OK", ImVec2(120, 0))) {
          ImGui::CloseCurrentPopup();
          globalErrorMessage = ""; // Clear message after user acknowledges
        }
        ImGui::EndPopup();
      }

      if (ImGui::BeginMainMenuBar()) {
        if (ImGui::BeginMenu("File")) {
          if (ImGui::MenuItem("Load Game"))
            show_load_game_window = true;
          if (ImGui::MenuItem("Game Browser"))
            show_game_browser = true;
          if (ImGui::MenuItem("Save State"))
            show_save_state_window = true;
          if (ImGui::MenuItem("Load State"))
            show_load_state_window = true;
          if (ImGui::MenuItem("Install PKG"))
            show_install_pkg_window = true;
          if (ImGui::MenuItem("Exit"))
            quit = true;
          ImGui::EndMenu();
        }
        if (ImGui::BeginMenu("Emulation")) {
          if (emulatorInitSuccess && emulator) {
            if (ImGui::MenuItem("Pause")) {
              emulator->Pause();
            }
            if (ImGui::MenuItem("Resume")) {
              emulator->Resume();
            } // Assuming Resume exists
            if (ImGui::MenuItem("Reset")) {
              emulator->Reset();
            } // Assuming Reset exists
          } else {
            ImGui::MenuItem("Pause", nullptr, false, false);  // Disabled
            ImGui::MenuItem("Resume", nullptr, false, false); // Disabled
            ImGui::MenuItem("Reset", nullptr, false, false);  // Disabled
          }

          // Add restart option if initialization failed
          if (emulatorInitDone && !emulatorInitSuccess) {
            if (ImGui::MenuItem("Restart Emulator")) {
              spdlog::info("Restarting emulator...");
              // Reset initialization state
              emulatorInitDone = false;
              emulatorInitSuccess = false;
              globalErrorMessage = "";

              // Shutdown existing emulator if it exists
              if (emulator) {
                emulator->Shutdown();
              }

              // Create new emulator and start initialization
              emulator = std::make_unique<PS4Emulator>();
              emulatorInitFuture = std::async(std::launch::async, [&]() {
                spdlog::info("Reinitializing PS4 emulator");
                try {
                  bool result = emulator->Initialize(window.get(), &vk_context);
                  return result;
                } catch (const std::exception &e) {
                  spdlog::error("Emulator reinitialization failed: {}",
                                e.what());
                  globalErrorMessage = fmt::format(
                      "Emulator reinitialization failed: {}", e.what());
                  return false;
                }
              });
              initStartTime = std::chrono::steady_clock::now(); // Reset timeout
            }
          }
          ImGui::EndMenu();
        }
        if (ImGui::BeginMenu("Settings")) {
          if (ImGui::MenuItem("Display Settings"))
            show_display_settings = true;
          if (ImGui::MenuItem("Audio Settings"))
            show_audio_settings = true;
          if (ImGui::MenuItem("CPU Settings"))
            show_cpu_settings = true;
          if (ImGui::MenuItem("GPU Settings"))
            show_gpu_settings = true;
          if (ImGui::MenuItem("Memory Settings"))
            show_memory_settings = true;
          if (ImGui::MenuItem("Filesystem Settings"))
            show_filesystem_settings = true;
          if (ImGui::MenuItem("Debug Settings"))
            show_debug_settings = true;
          if (ImGui::MenuItem("Input Settings"))
            show_input_settings = true;
          if (ImGui::MenuItem("Network Settings"))
            show_network_settings = true;
          if (ImGui::MenuItem("Compatibility Settings"))
            show_compatibility_settings = true;
          if (ImGui::MenuItem("Advanced Settings"))
            show_advanced_settings = true;
          ImGui::EndMenu();
        }
        if (ImGui::BeginMenu("Tools")) {
          if (ImGui::MenuItem("Filesystem Explorer"))
            show_filesystem_window = true;
          if (ImGui::MenuItem("Diagnostics"))
            show_diagnostics_window = true;
          if (ImGui::MenuItem("Input Monitor"))
            show_input_window = true;
          if (ImGui::MenuItem("Font Debug"))
            show_font_debug_window = true;
          if (ImGui::MenuItem("Performance Overlay"))
            show_performance_overlay = true;
          ImGui::EndMenu();
        }
        if (ImGui::BeginMenu("Help")) {
          if (ImGui::MenuItem("Show Demo Window"))
            show_demo_window = true;
          ImGui::EndMenu();
        }
        ImGui::EndMainMenuBar();
      }

      if (show_demo_window)
        ImGui::ShowDemoWindow(&show_demo_window);

      if (show_another_window) {
        ImGui::Begin("Another Window", &show_another_window);
        ImGui::Text("Hello from another window!");
        if (ImGui::Button("Close Me"))
          show_another_window = false;
        ImGui::End();
      }

      if (show_load_game_window) {
        ImGui::Begin("Load Game", &show_load_game_window);
        DrawSettingInputText("Game Path", gamePathBuf, sizeof(gamePathBuf));
        ImGui::SameLine();
        if (ImGui::Button("Browse...")) {
          std::string path = OpenFileDialog(
              "PS4 Game Files\0*.pkg;*.iso;*.bin\0All Files\0*.*\0",
              "Select PS4 Game File");
          if (!path.empty()) {
            std::strncpy(gamePathBuf, path.c_str(), sizeof(gamePathBuf));
            gamePathBuf[sizeof(gamePathBuf) - 1] = '\0';
          }
        }
        if (ImGui::Button("Load")) {
          std::string gamePath = gamePathBuf;
          if (emulator) {
            auto loadFuture =
                std::async(std::launch::async, [&emulator, gamePath]() {
                  return emulator->LoadGame(gamePath);
                });
            if (loadFuture.get()) {
              std::unique_lock<std::shared_mutex> lock(settingsMutex);
              settings.last_game_path = gamePath;
              // Remove if already in recent games to move to top
              settings.recent_games.erase(
                  std::remove(settings.recent_games.begin(),
                              settings.recent_games.end(), gamePath),
                  settings.recent_games.end());
              settings.recent_games.insert(settings.recent_games.begin(),
                                           gamePath);
              if (settings.recent_games.size() > 10)
                settings.recent_games.pop_back(); // Limit recent games
              SaveSettings();
              spdlog::info("Game loaded: {}", gamePath);
            } else {
              spdlog::error("Failed to load game: {}", gamePath);
              globalErrorMessage =
                  fmt::format("Failed to load game: {}", gamePath);
            }
          } else {
            spdlog::error("Emulator not initialized, cannot load game.");
            globalErrorMessage = "Emulator not initialized, cannot load game.";
          }
          show_load_game_window = false;
        }
        ImGui::SameLine();
        if (ImGui::Button("Cancel"))
          show_load_game_window = false;

        ImGui::Separator();
        ImGui::Text("Recent Games:");
        if (settings.recent_games.empty()) {
          ImGui::Text("No recent games.");
        } else {
          for (const auto &recent_game : settings.recent_games) {
            if (ImGui::MenuItem(recent_game.c_str())) {
              std::strncpy(gamePathBuf, recent_game.c_str(),
                           sizeof(gamePathBuf));
              gamePathBuf[sizeof(gamePathBuf) - 1] = '\0';
            }
          }
        }
        ImGui::End();
      }

      if (show_save_state_window) {
        ImGui::Begin("Save State", &show_save_state_window);
        DrawSettingInputText("State Path", statePathBuf, sizeof(statePathBuf));
        ImGui::SameLine();
        if (ImGui::Button("Browse...##SaveState")) {
          std::string path =
              OpenFileDialog("Emulator Save State\0*.sav\0All Files\0*.*\0",
                             "Save Emulator State As");
          if (!path.empty()) {
            std::strncpy(statePathBuf, path.c_str(), sizeof(statePathBuf));
            statePathBuf[sizeof(statePathBuf) - 1] = '\0';
          }
        }
        if (ImGui::Button("Save")) {
          std::string statePath = statePathBuf;
          if (emulator) {
            auto saveFuture =
                std::async(std::launch::async, [&emulator, statePath]() {
                  try {
                    emulator->SaveState(statePath);
                    return true;
                  } catch (const std::exception &e) {
                    spdlog::error("Failed to save state to {}: {}", statePath,
                                  e.what());
                    globalErrorMessage =
                        fmt::format("Failed to save state: {}", e.what());
                    return false;
                  }
                });
            if (saveFuture.get()) {
              spdlog::info("State saved to {}", statePath);
            } else {
              spdlog::error("Failed to save state to {}", statePath);
              globalErrorMessage =
                  fmt::format("Failed to save state to {}", statePath);
            }
          } else {
            spdlog::error("Emulator not initialized, cannot save state.");
            globalErrorMessage = "Emulator not initialized, cannot save state.";
          }
          show_save_state_window = false;
        }
        ImGui::SameLine();
        if (ImGui::Button("Cancel"))
          show_save_state_window = false;
        ImGui::End();
      }

      if (show_load_state_window) {
        ImGui::Begin("Load State", &show_load_state_window);
        DrawSettingInputText("State Path", statePathBuf, sizeof(statePathBuf));
        ImGui::SameLine();
        if (ImGui::Button("Browse...##LoadState")) {
          std::string path =
              OpenFileDialog("Emulator Save State\0*.sav\0All Files\0*.*\0",
                             "Load Emulator State From");
          if (!path.empty()) {
            std::strncpy(statePathBuf, path.c_str(), sizeof(statePathBuf));
            statePathBuf[sizeof(statePathBuf) - 1] = '\0';
          }
        }
        if (ImGui::Button("Load")) {
          std::string statePath = statePathBuf;
          if (emulator) {
            auto loadFuture =
                std::async(std::launch::async, [&emulator, statePath]() {
                  try {
                    emulator->LoadState(statePath);
                    return true;
                  } catch (const std::exception &e) {
                    spdlog::error("Failed to load state from {}: {}", statePath,
                                  e.what());
                    globalErrorMessage =
                        fmt::format("Failed to load state: {}", e.what());
                    return false;
                  }
                });
            if (loadFuture.get()) {
              spdlog::info("State loaded from {}", statePath);
            } else {
              spdlog::error("Failed to load state from {}", statePath);
              globalErrorMessage =
                  fmt::format("Failed to load state from {}", statePath);
            }
          } else {
            spdlog::error("Emulator not initialized, cannot load state.");
            globalErrorMessage = "Emulator not initialized, cannot load state.";
          }
          show_load_state_window = false;
        }
        ImGui::SameLine();
        if (ImGui::Button("Cancel"))
          show_load_state_window = false;
        ImGui::End();
      }

      if (show_filesystem_window) {
        ImGui::Begin("Filesystem", &show_filesystem_window);
        if (emulator) {
          ImGui::Text("Filesystem State:");
          ImGui::TextWrapped("%s",
                             emulator->GetFilesystem().DumpState().c_str());
          static char dirPathBuf[260] = "/app0";
          DrawSettingInputText("Directory Path", dirPathBuf,
                               sizeof(dirPathBuf));
          if (ImGui::Button("Create Directory")) {
            try {
              emulator->GetFilesystem().CreateVirtualDirectory(dirPathBuf);
              spdlog::info("Created directory {} in emulated filesystem",
                           dirPathBuf);
            } catch (const std::exception &e) {
              spdlog::error("Failed to create directory {}: {}", dirPathBuf,
                            e.what());
              globalErrorMessage =
                  fmt::format("Failed to create directory: {}", e.what());
            }
          }
        } else {
          ImGui::Text("Emulator not initialized.");
        }
        ImGui::End();
      }

      if (show_diagnostics_window) {
        ImGui::Begin("Diagnostics", &show_diagnostics_window);
        if (emulator) {
          ImGui::Text("Emulator Stats:");
          auto emuStats = emulator->GetStats();
          ImGui::Text("Instructions Executed: %llu",
                      emuStats.instructionsExecuted.load());
          ImGui::Text("Total Cycles: %llu", emuStats.totalCycles.load());
          ImGui::Text("Total Latency: %llu us", emuStats.totalLatencyUs.load());
          ImGui::Text("Memory Diagnostics:");
          for (const auto &[key, value] :
               MemoryDiagnostics::GetInstance().GetMetrics()) {
            ImGui::Text("%s: %llu", key.c_str(), value);
          }
          ImGui::Text("CPU Diagnostics:");
          for (const auto &[key, value] :
               x86_64::CPUDiagnostics::GetInstance().GetMetrics()) {
            ImGui::Text("%s: %llu", key.c_str(), value);
          }
          ImGui::Text("JIT Diagnostics:");
          for (const auto &[key, value] :
               x86_64::JITDiagnostics::GetInstance().GetMetrics()) {
            ImGui::Text("%s: %llu", key.c_str(), value);
          }
          ImGui::Text("GPU Stats:");
          auto gpuStats = emulator->GetGPU().GetStats();
          ImGui::Text("Shader Count: %llu", gpuStats.shaderCount.load());
          ImGui::Text("Draw Count: %llu", gpuStats.drawCount.load());
          ImGui::Text("Total Latency: %llu us", gpuStats.totalLatencyUs.load());
          ImGui::Text("Cache Hits: %llu", gpuStats.cacheHits.load());
          ImGui::Text("Cache Misses: %llu", gpuStats.cacheMisses.load());
          ImGui::Text("Errors: %llu", gpuStats.errorCount.load());
          ImGui::Text("Shader Translator Stats:");
          ImGui::Text("(Shader translator stats integrated into GPU stats)");
          ImGui::Text("Tile Manager Stats:");
          auto tileStats = emulator->GetTileManager().GetStats();
          ImGui::Text("Operation Count: %llu", tileStats.operationCount);
          ImGui::Text("Total Latency: %llu us", tileStats.totalLatencyUs);
          ImGui::Text("Cache Hits: %llu", tileStats.cacheHits);
          ImGui::Text("Cache Misses: %llu", tileStats.cacheMisses);
          ImGui::Text("Errors: %llu", tileStats.errorCount);
          ImGui::Text("Command Processor Stats:");
          auto cmdStats = emulator->GetCommandProcessor().GetStats();
          ImGui::Text("Packet Count: %llu", cmdStats.packetCount);
          ImGui::Text("Total Latency: %llu us", cmdStats.totalLatencyUs);
          ImGui::Text("Cache Hits: %llu", cmdStats.cacheHits);
          ImGui::Text("Cache Misses: %llu", cmdStats.cacheMisses);
          ImGui::Text("Errors: %llu", cmdStats.errorCount);
          ImGui::Text("Vulkan Context Stats:");
          ImGui::Text("Frame Count: %llu", vk_context.frameCount);
          ImGui::Text("Render Latency: %llu us", vk_context.renderLatencyUs);
          ImGui::Text("Cache Hits: %llu", vk_context.cacheHits);
          ImGui::Text("Cache Misses: %llu", vk_context.cacheMisses);
        } else {
          ImGui::Text("Emulator not initialized.");
        }
        ImGui::End();
      }

      if (show_font_debug_window) {
        ImGui::Begin("Font Debug", &show_font_debug_window);
        ImGui::Text("Font Metrics and Atlas Information");
        ImGui::Separator();
        ImGuiIO &io = ImGui::GetIO();
        ImGui::Text("Display Scale: %.2f", io.DisplayFramebufferScale.x);
        ImGui::Text("Font Global Scale: %.2f", io.FontGlobalScale);
        if (io.FontDefault) {
          ImGui::Text("Default Font Information:");
          ImGui::Indent();
          ImGui::Text("Font Size: %.1f", io.FontDefault->FontSize);
          ImGui::Text("Scale: %.2f", io.FontDefault->Scale);
          ImGui::Text("Ascent: %.1f", io.FontDefault->Ascent);
          ImGui::Text("Descent: %.1f", io.FontDefault->Descent);
          ImGui::Text("Height: %.1f (Ascent - Descent: %.1f)",
                      io.FontDefault->Ascent - io.FontDefault->Descent,
                      io.FontDefault->Ascent + (-io.FontDefault->Descent));
          ImGui::Text("Fallback Advance X: %.1f",
                      io.FontDefault->FallbackAdvanceX);
          ImGui::Text("Fallback Character: U+%04X",
                      io.FontDefault->FallbackChar);
          ImGui::Text("Ellipsis Character: U+%04X",
                      io.FontDefault->EllipsisChar);
          ImGui::Text("Container Atlas: %p", io.FontDefault->ContainerAtlas);
          ImGui::Text("Sources Count: %d", io.FontDefault->SourcesCount);
          ImGui::Unindent();
        }
        ImGui::Separator();
        if (io.Fonts) {
          ImGui::Text("Font Atlas Information:");
          ImGui::Indent();
          ImGui::Text("Atlas Size: %dx%d", io.Fonts->TexWidth,
                      io.Fonts->TexHeight);
          ImGui::Text("Font Count: %d", io.Fonts->Fonts.Size);
          ImGui::Text("Flags: 0x%08X", io.Fonts->Flags);
          ImGui::Text("Texture ID: %llu", (unsigned long long)io.Fonts->TexID);
          ImGui::Text("Texture Desired Width: %d", io.Fonts->TexDesiredWidth);
          ImGui::Text("Texture Glyph Padding: %d", io.Fonts->TexGlyphPadding);
          ImGui::Text("Locked: %s", io.Fonts->Locked ? "Yes" : "No");
          ImGui::Unindent();
          ImGui::Separator();
          ImGui::Text("Loaded Fonts:");
          for (int i = 0; i < io.Fonts->Fonts.Size; i++) {
            ImFont *font = io.Fonts->Fonts[i];
            ImGui::PushID(i);
            if (ImGui::TreeNode("Font", "Font %d: %.1fpx", i, font->FontSize)) {
              ImGui::Text("Font Size: %.1f", font->FontSize);
              ImGui::Text("Scale: %.2f", font->Scale);
              ImGui::Text("Ascent: %.1f", font->Ascent);
              ImGui::Text("Descent: %.1f", font->Descent);
              ImGui::Text("Glyphs Count: %d", font->Glyphs.Size);
              ImGui::Text("Index Advance X Count: %d",
                          font->IndexAdvanceX.Size);
              ImGui::Text("Index Lookup Count: %d", font->IndexLookup.Size);
              ImGui::Text("Sources Count: %d", font->SourcesCount);
              ImGui::Text("Metrics Total Surface: %d",
                          font->MetricsTotalSurface);
              if (font->Sources && font->SourcesCount > 0) {
                ImGui::Text("Font Sources:");
                ImGui::Indent();
                for (int j = 0; j < font->SourcesCount; j++) {
                  const ImFontConfig *cfg = &font->Sources[j];
                  ImGui::Text("Source %d:", j);
                  ImGui::Indent();
                  ImGui::Text("Size Pixels: %.1f", cfg->SizePixels);
                  ImGui::Text("Oversample H: %d, V: %d", cfg->OversampleH,
                              cfg->OversampleV);
                  ImGui::Text("Pixel Snap H: %s",
                              cfg->PixelSnapH ? "Yes" : "No");
                  ImGui::Text("Glyph Offset: %.1f, %.1f", cfg->GlyphOffset.x,
                              cfg->GlyphOffset.y);
                  ImGui::Text("Glyph Min/Max Advance X: %.1f, %.1f",
                              cfg->GlyphMinAdvanceX, cfg->GlyphMaxAdvanceX);
                  ImGui::Text("Merge Mode: %s", cfg->MergeMode ? "Yes" : "No");
                  ImGui::Text("Font Builder Flags: 0x%08X",
                              cfg->FontBuilderFlags);
                  ImGui::Text("Rasterizer Multiply: %.2f",
                              cfg->RasterizerMultiply);
                  ImGui::Text("Rasterizer Density: %.2f",
                              cfg->RasterizerDensity);
                  if (cfg->Name[0]) {
                    ImGui::Text("Name: %s", cfg->Name);
                  }
                  ImGui::Unindent();
                }
                ImGui::Unindent();
              }
              ImGui::Separator();
              ImGui::Text("Font Sample:");
              ImGui::Indent();
              ImGui::PushFont(font);
              ImGui::Text("The quick brown fox jumps over the lazy dog");
              ImGui::Text("ABCDEFGHIJKLMNOPQRSTUVWXYZ");
              ImGui::Text("abcdefghijklmnopqrstuvwxyz");
              ImGui::Text("0123456789 !@#$%%^&*()");
              ImGui::PopFont();
              ImGui::Unindent();
              ImGui::TreePop();
            }
            ImGui::PopID();
          }
          ImGui::Separator();
          ImGui::Text("Font Atlas Texture:");
          if (io.Fonts->TexID) {
            static float atlas_scale = 1.0f;
            ImGui::SliderFloat("Atlas Scale", &atlas_scale, 0.1f, 3.0f, "%.1f");
            float tex_w = (float)io.Fonts->TexWidth;
            float tex_h = (float)io.Fonts->TexHeight;
            ImVec2 display_size =
                ImVec2(tex_w * atlas_scale, tex_h * atlas_scale);
            if (display_size.x > 0 && display_size.y > 0) {
              ImGui::Image(io.Fonts->TexID, display_size);
            }
          } else {
            ImGui::Text("Atlas texture not available");
          }
        }
        ImGui::End();
      }

      if (show_input_window) {
        ImGui::Begin("Input", &show_input_window);
        if (emulator) {
          auto &controllerManager = emulator->GetControllerManager();
          for (int i = 0; i < controllerManager.GetControllerCount(); ++i) {
            ImGui::Text("Controller %d:", i);
            ImGui::Text(
                "Buttons: A=%d, B=%d, X=%d, Y=%d",
                controllerManager.GetButtonState(i, SDL_CONTROLLER_BUTTON_A),
                controllerManager.GetButtonState(i, SDL_CONTROLLER_BUTTON_B),
                controllerManager.GetButtonState(i, SDL_CONTROLLER_BUTTON_X),
                controllerManager.GetButtonState(i, SDL_CONTROLLER_BUTTON_Y));
            ImGui::Text(
                "Left Stick: X=%.2f, Y=%.2f",
                controllerManager.GetAxisState(i, SDL_CONTROLLER_AXIS_LEFTX) /
                    32768.0f,
                controllerManager.GetAxisState(i, SDL_CONTROLLER_AXIS_LEFTY) /
                    32768.0f);
          }
        } else {
          ImGui::Text("Emulator not initialized.");
        }
        if (inputManager) {
          auto inputStats = inputManager->GetStats();
          ImGui::Text("Input Stats: Events=%llu, Latency=%llu us",
                      inputStats.eventCount, inputStats.totalLatencyUs);
        }
        ImGui::End();
      }

      if (show_install_pkg_window) {
        ImGui::Begin("Install PKG File", &show_install_pkg_window);
        static char pkgPathBuf[260] = "";
        static char installPathBuf[260] = "/mnt/sandbox/pfsmnt";
        static std::string statusMessage = "";
        static bool isInstalling = false;
        static float installProgress = 0.0f;
        static std::future<bool> installFuture;
        static std::string pkgContentId, pkgVersion, pkgTitle;
        static bool pkgInfoLoaded = false;

        ImGui::Text("Select PKG file to install:");
        DrawSettingInputText("PKG Path", pkgPathBuf, sizeof(pkgPathBuf));
        ImGui::SameLine();
        if (ImGui::Button("Browse PKG...")) {
          std::string path = OpenFileDialog(
              "PKG Files\0*.pkg\0All Files\0*.*\0", "Select PKG File");
          if (!path.empty()) {
            std::strncpy(pkgPathBuf, path.c_str(), sizeof(pkgPathBuf));
            pkgPathBuf[sizeof(pkgPathBuf) - 1] = '\0';
            pkgInfoLoaded = false;
            statusMessage = "";
          }
        }

        if (strlen(pkgPathBuf) > 0 && !pkgInfoLoaded && !isInstalling &&
            emulator) {
          try {
            if (emulator->GetPKGInstaller().GetPKGInfo(pkgPathBuf, pkgContentId,
                                                       pkgVersion, pkgTitle)) {
              pkgInfoLoaded = true;
              statusMessage = "PKG information loaded successfully";
            } else {
              statusMessage =
                  "Failed to load PKG information - file may be invalid";
            }
          } catch (const std::exception &e) {
            statusMessage = fmt::format("Error loading PKG info: {}", e.what());
            globalErrorMessage = statusMessage;
          }
        }

        if (pkgInfoLoaded) {
          ImGui::Separator();
          ImGui::Text("PKG Information:");
          ImGui::Text("Title: %s", pkgTitle.c_str());
          ImGui::Text("Content ID: %s", pkgContentId.c_str());
          ImGui::Text("Version: %s", pkgVersion.c_str());
          ImGui::Separator();
        }

        ImGui::Text("Installation path:");
        DrawSettingInputText("Install Path", installPathBuf,
                             sizeof(installPathBuf));

        if (!statusMessage.empty()) {
          ImGui::Separator();
          ImGui::TextWrapped("Status: %s", statusMessage.c_str());
        }

        if (isInstalling) {
          ImGui::Separator();
          ImGui::Text("Installing...");
          ImGui::ProgressBar(installProgress, ImVec2(-1.0f, 0.0f));
          if (installFuture.valid() &&
              installFuture.wait_for(std::chrono::milliseconds(0)) ==
                  std::future_status::ready) {
            try {
              bool success = installFuture.get();
              if (success) {
                statusMessage = "PKG installed successfully!";
                spdlog::info("PKG {} installed successfully to {}", pkgPathBuf,
                             installPathBuf);
              } else {
                statusMessage = "PKG installation failed!";
                spdlog::error("Failed to install PKG {}", pkgPathBuf);
                globalErrorMessage = statusMessage;
              }
            } catch (const std::exception &e) {
              statusMessage = fmt::format("Installation error: {}", e.what());
              spdlog::error("PKG installation exception: {}", e.what());
              globalErrorMessage = statusMessage;
            }
            isInstalling = false;
            installProgress = 0.0f;
          } else {
            // Simulate progress if actual progress is not available
            installProgress = std::min(installProgress + 0.005f, 0.99f);
          }
        } else {
          ImGui::Separator();
          if (ImGui::Button("Install PKG") && strlen(pkgPathBuf) > 0) {
            if (emulator) {
              statusMessage = "Starting installation...";
              isInstalling = true;
              installProgress = 0.0f;
              installFuture = std::async(
                  std::launch::async,
                  [emulatorPtr = emulator.get(),
                   pkgPath = std::string(pkgPathBuf),
                   installPath = std::string(installPathBuf)]() {
                    try {
                      return emulatorPtr->GetPKGInstaller().InstallPKG(
                          pkgPath, installPath);
                    } catch (const std::exception &e) {
                      spdlog::error("PKG installation failed: {}", e.what());
                      return false;
                    }
                  });
            } else {
              statusMessage = "Emulator not initialized";
              globalErrorMessage = statusMessage;
            }
          }
          ImGui::SameLine();
          if (ImGui::Button("Validate PKG") && strlen(pkgPathBuf) > 0) {
            if (emulator) {
              try {
                bool isValid =
                    emulator->GetPKGInstaller().ValidatePKG(pkgPathBuf);
                statusMessage = isValid ? "PKG file is valid"
                                        : "PKG file is invalid or corrupted";
              } catch (const std::exception &e) {
                statusMessage = fmt::format("Validation error: {}", e.what());
                globalErrorMessage = statusMessage;
              }
            }
          }
        }
        ImGui::Separator();
        if (ImGui::Button("Close"))
          show_install_pkg_window = false;
        ImGui::End();
      }

      if (show_set_game_directory_window) {
        ImGui::Begin("Set Game Directory", &show_set_game_directory_window);
        static char gameDirectoryBuf[260] = "";
        static std::string statusMessage = "";
        static std::vector<std::string> installedPackages;
        static bool packagesLoaded = false;
        static std::future<std::vector<std::string>> packagesFuture;
        static bool packagesLoading = false;

        if (!packagesLoaded && !packagesLoading && emulator) {
          packagesLoading = true;
          statusMessage = "Loading installed packages...";
          spdlog::info("Starting async load of installed packages list...");
          packagesFuture = std::async(std::launch::async, [&emulator]() {
            try {
              return emulator->GetPKGInstaller().ListInstalledPackages();
            } catch (const std::exception &e) {
              spdlog::error("Failed to load installed packages: {}", e.what());
              globalErrorMessage = fmt::format(
                  "Failed to load installed packages: {}", e.what());
              return std::vector<std::string>{};
            }
          });
        }

        if (packagesLoading && packagesFuture.valid()) {
          if (packagesFuture.wait_for(std::chrono::milliseconds(0)) ==
              std::future_status::ready) {
            try {
              installedPackages = packagesFuture.get();
              packagesLoaded = true;
              packagesLoading = false;
              if (installedPackages.empty()) {
                statusMessage = "No installed packages found";
              } else {
                statusMessage = fmt::format("Found {} installed package(s)",
                                            installedPackages.size());
              }
              spdlog::info("Installed packages loaded successfully");
            } catch (const std::exception &e) {
              statusMessage =
                  fmt::format("Error loading packages: {}", e.what());
              spdlog::error("Failed to load installed packages: {}", e.what());
              globalErrorMessage = statusMessage;
              packagesLoaded = true;
              packagesLoading = false;
            }
          }
        }

        ImGui::Text("Current game directory:");
        if (emulator) {
          std::string currentDir = emulator->GetFilesystem().GetGameDirectory();
          ImGui::TextWrapped("%s", currentDir.c_str());
        }

        ImGui::Separator();
        ImGui::Text("Select new game directory:");
        DrawSettingInputText("Directory Path", gameDirectoryBuf,
                             sizeof(gameDirectoryBuf));
        ImGui::SameLine();
        if (ImGui::Button("Browse...")) {
          std::string path = OpenFolderDialog("Select Game Directory");
          if (!path.empty()) {
            std::strncpy(gameDirectoryBuf, path.c_str(),
                         sizeof(gameDirectoryBuf));
            gameDirectoryBuf[sizeof(gameDirectoryBuf) - 1] = '\0';
          }
        }

        if (ImGui::Button("Set Directory") && strlen(gameDirectoryBuf) > 0) {
          if (emulator) {
            try {
              spdlog::info("Setting game directory to: {}", gameDirectoryBuf);
              if (!std::filesystem::exists(gameDirectoryBuf)) {
                statusMessage = "Error: Directory does not exist";
                spdlog::error("Game directory does not exist: {}",
                              gameDirectoryBuf);
                globalErrorMessage = statusMessage;
              } else if (!std::filesystem::is_directory(gameDirectoryBuf)) {
                statusMessage = "Error: Path is not a directory";
                spdlog::error("Game directory path is not a directory: {}",
                              gameDirectoryBuf);
                globalErrorMessage = statusMessage;
              } else {
                emulator->GetFilesystem().SetGameDirectory(gameDirectoryBuf);
                statusMessage = "Game directory updated successfully";
                spdlog::info("Game directory set successfully");
                std::unique_lock<std::shared_mutex> lock(settingsMutex);
                settings.game_directory = gameDirectoryBuf;
                SaveSettings();
                packagesLoaded = false;
                packagesLoading = false;
                game_list
                    .clear(); // Clear old game list, GameBrowser will re-scan
              }
            } catch (const std::exception &e) {
              statusMessage =
                  fmt::format("Error setting directory: {}", e.what());
              spdlog::error("Failed to set game directory: {}", e.what());
              globalErrorMessage = statusMessage;
            }
          }
        }

        if (!statusMessage.empty()) {
          ImGui::Separator();
          ImGui::TextWrapped("Status: %s", statusMessage.c_str());
        }

        if (!installedPackages.empty()) {
          ImGui::Separator();
          ImGui::Text("Installed Packages:");
          static int selectedPackage = -1;
          for (int i = 0; i < installedPackages.size(); ++i) {
            if (ImGui::Selectable(installedPackages[i].c_str(),
                                  selectedPackage == i)) {
              selectedPackage = i;
            }
          }
          if (selectedPackage >= 0 &&
              selectedPackage < installedPackages.size()) {
            ImGui::Separator();
            ImGui::Text("Selected: %s",
                        installedPackages[selectedPackage].c_str());
            if (ImGui::Button("Uninstall Selected")) {
              if (emulator) {
                try {
                  bool success = emulator->GetPKGInstaller().UninstallPackage(
                      installedPackages[selectedPackage]);
                  if (success) {
                    statusMessage = "Package uninstalled successfully";
                    packagesLoaded = false;
                    packagesLoading = false;
                    selectedPackage = -1;
                  } else {
                    statusMessage = "Failed to uninstall package";
                    globalErrorMessage = statusMessage;
                  }
                } catch (const std::exception &e) {
                  statusMessage = fmt::format("Uninstall error: {}", e.what());
                  globalErrorMessage = statusMessage;
                }
              }
            }
          }
        }

        ImGui::Separator();
        if (ImGui::Button("Refresh Packages")) {
          packagesLoaded = false;
          packagesLoading = false;
          statusMessage = "Refreshing package list...";
        }
        ImGui::SameLine();
        if (ImGui::Button("Close"))
          show_set_game_directory_window = false;
        ImGui::End();
      }

      // Draw all settings windows using helper functions
      if (show_display_settings)
        DrawDisplaySettingsUI(window.get(), emulator.get());
      if (show_audio_settings)
        DrawAudioSettingsUI(emulator.get());
      if (show_cpu_settings)
        DrawCPUSettingsUI(emulator.get());
      if (show_gpu_settings)
        DrawGPUSettingsUI(emulator.get());
      if (show_memory_settings)
        DrawMemorySettingsUI(emulator.get());
      if (show_filesystem_settings)
        DrawFilesystemSettingsUI(emulator.get());
      if (show_debug_settings)
        DrawDebugSettingsUI(emulator.get());
      if (show_input_settings)
        DrawInputSettingsUI(emulator.get());
      if (show_network_settings)
        DrawNetworkSettingsUI(emulator.get());
      if (show_compatibility_settings)
        DrawCompatibilitySettingsUI(emulator.get());
      if (show_advanced_settings)
        DrawAdvancedSettingsUI(emulator.get());

      // Game Browser Window - Allow showing even during emulator initialization
      if (show_game_browser) {
        if (gameBrowser) {
          gameBrowser->Draw(&show_game_browser); // Pass pointer to bool to
                                                 // allow closing from within
          if (!show_game_browser) {              // If GameBrowser closed itself
            // Handle selected game from GameBrowser if any
            std::string gameToLoad = gameBrowser->GetSelectedGamePath();
            if (!gameToLoad.empty()) {
              spdlog::info("Game selected from browser: {}", gameToLoad);
              if (emulator && emulatorInitSuccess) {
                auto loadFuture =
                    std::async(std::launch::async, [&emulator, gameToLoad]() {
                      return emulator->LoadGame(gameToLoad);
                    });
                if (loadFuture.get()) {
                  std::unique_lock<std::shared_mutex> lock(settingsMutex);
                  settings.last_game_path = gameToLoad;
                  settings.recent_games.erase(
                      std::remove(settings.recent_games.begin(),
                                  settings.recent_games.end(), gameToLoad),
                      settings.recent_games.end());
                  settings.recent_games.insert(settings.recent_games.begin(),
                                               gameToLoad);
                  if (settings.recent_games.size() > 10)
                    settings.recent_games.pop_back();
                  SaveSettings();
                  spdlog::info("Game loaded: {}", gameToLoad);
                } else {
                  spdlog::error("Failed to load game: {}", gameToLoad);
                  globalErrorMessage =
                      fmt::format("Failed to load game: {}", gameToLoad);
                }
              } else {
                spdlog::warn("Cannot load game - emulator not ready yet");
                globalErrorMessage = "Emulator is still initializing. Please "
                                     "wait and try again.";
              }
              gameBrowser->ClearSelectedGamePath(); // Clear selection after
                                                    // attempting to load
            }
          }
        } else {
          // Show a simple game browser even if emulator isn't ready
          ImGui::Begin("Game Browser", &show_game_browser);
          if (!emulatorInitDone) {
            ImGui::Text("Emulator is initializing...");
            ImGui::Text("Game browser will be available once initialization "
                        "completes.");
          } else if (!emulatorInitSuccess) {
            ImGui::Text("Emulator initialization failed.");
            ImGui::Text("Cannot browse games.");
          } else {
            ImGui::Text("Game browser is loading...");
          }
          ImGui::End();
        }
      }

      // Performance Overlay Window
      if (show_performance_overlay && performanceOverlay) {
        performanceOverlay->Draw(&show_performance_overlay);
      }

      if (!show_demo_window && !show_another_window && !show_load_game_window &&
          !show_save_state_window && !show_load_state_window &&
          !show_filesystem_window && !show_diagnostics_window &&
          !show_input_window && !show_font_debug_window &&
          !show_display_settings && !show_audio_settings &&
          !show_cpu_settings && !show_gpu_settings && !show_memory_settings &&
          !show_filesystem_settings && !show_debug_settings &&
          !show_input_settings && !show_network_settings &&
          !show_compatibility_settings && !show_advanced_settings &&
          !show_game_browser && !show_performance_overlay &&
          !show_install_pkg_window && !show_set_game_directory_window) {
        ImGui::Begin("Emulator Status", nullptr,
                     ImGuiWindowFlags_AlwaysAutoResize |
                         ImGuiWindowFlags_NoCollapse);

        if (!emulatorInitDone) {
          ImGui::Text("PS4 Emulator is initializing...");

          // Show initialization progress
          auto currentTime = std::chrono::steady_clock::now();
          auto elapsed = std::chrono::duration_cast<std::chrono::seconds>(
              currentTime - initStartTime);
          ImGui::Text("Elapsed time: %d seconds",
                      static_cast<int>(elapsed.count()));

          // Show a progress bar (indeterminate)
          static float progress = 0.0f;
          progress += 0.01f;
          if (progress > 1.0f)
            progress = 0.0f;
          ImGui::ProgressBar(progress, ImVec2(0.0f, 0.0f), "Initializing...");

          ImGui::Text("Please wait while the emulator starts up.");
          ImGui::Text("This may take up to 30 seconds.");
        } else if (!emulatorInitSuccess) {
          ImGui::Text("PS4 Emulator initialization failed.");
          ImGui::Text("Check the error message above for details.");
          ImGui::Text(
              "You can try restarting the emulator from the Emulation menu.");
        } else {
          ImGui::Text("PS4 Emulator is running.");
          ImGui::Text("No game loaded.");
          ImGui::Text("Use File -> Game Browser to select a game.");
        }
        ImGui::End();
      }

      ImGui::Render();
      ImDrawData *draw_data = ImGui::GetDrawData();
      bool skipFrame =
          draw_data->DisplaySize.x <= 0.0f || draw_data->DisplaySize.y <= 0.0f;
      uint32_t image_index = 0;
      if (!skipFrame) {
        renderFrame(vk_context, render_command_buffer, draw_data, image_index,
                    swapchain_rebuild, quit, *emulator);
      }

      auto now = std::chrono::steady_clock::now();
      auto elapsed = std::chrono::duration_cast<std::chrono::milliseconds>(
          now - lastFrame);
      if (elapsed < frameTime)
        std::this_thread::sleep_for(frameTime - elapsed);
      lastFrame = now;
    }

    if (emulator)
      emulator->Shutdown();
    ImGui_ImplVulkan_Shutdown();
    ImGui_ImplSDL2_Shutdown();
    ImGui::DestroyContext();
    cleanupVulkan(vk_context);

    delete gameBrowser;
    gameBrowser = nullptr;
    delete inputSettings;
    inputSettings = nullptr;
    delete performanceOverlay;
    performanceOverlay = nullptr;

    SDL_Quit();
    return 0;
  } catch (const std::exception &e) {
    spdlog::error("Unhandled exception in main: {}", e.what());
    globalErrorMessage = fmt::format("Critical Error: {}", e.what());
    // In a real application, you might want to show a message box here
    // before exiting, especially for unhandled exceptions.
    return EXIT_FAILURE;
  }
  return EXIT_SUCCESS;
}
} // namespace ps4

int SDL_main(int argc, char *argv[]) { return ps4::main(); }

#ifndef SDL_MAIN_HANDLED
int main(int argc, char *argv[]) { return ps4::main(); }
#endif
