#ifndef CPU_INSTRUCTION_DECODER_H
#define CPU_INSTRUCTION_DECODER_H

#include "decoded_instruction.h"
#include "register.h"
#include <array>
#include <cstdint>
#include <string>
#include <unordered_map>
#include <vector>

namespace x86_64 {

// REX prefix bits
const uint8_t REX_W = 0b1000; // 64-bit operand size
const uint8_t REX_R = 0b0100; // Extension of the ModR/M reg field
const uint8_t REX_X = 0b0010; // Extension of the SIB index field
const uint8_t REX_B = 0b0001; // Extension of the ModR/M r/m field, SIB base
                              // field, or Opcode reg field

// Enumeration of all possible instruction types the decoder can identify.

// Error codes for the decoder.
enum class DecoderError {
  Success,
  BufferOverflow,
  IncompleteInstruction,
  InvalidInstruction,
  UnknownOpcode
};

// Structure to hold detailed error information.
struct DecoderErrorInfo {
  DecoderError error;
  std::string message;
};

// Represents a single operand of an instruction.
struct Operand {
  enum class Type {
    NONE,
    REGISTER,
    MEMORY,
    IMMEDIATE,
    SEGMENT,
    CONTROL,
    DEBUG,
    MMX,
    XMM,
    YMM,
    ZMM,
    ST
  };

  struct Memory {
    Register base = Register::NONE;
    Register index = Register::NONE;
    uint8_t scale = 1;
    int64_t displacement = 0;

    void reset() {
      base = Register::NONE;
      index = Register::NONE;
      scale = 1;
      displacement = 0;
    }
  };

  Type type = Type::NONE;
  Register reg = Register::NONE;
  Memory memory;
  uint64_t immediate = 0;
  uint8_t size = 0; // in bits

  void reset() {
    type = Type::NONE;
    reg = Register::NONE;
    memory.reset();
    immediate = 0;
    size = 0;
  }
};

// VEX prefix data
struct VexPrefix {
  uint8_t raw[3] = {0, 0, 0};
};

// Constants for operand and prefix array sizes
static constexpr size_t MAX_OPERANDS = 4;
static constexpr size_t MAX_PREFIXES = 4;
// Main class for decoding x86-64 instructions.
class InstructionDecoder {
public:
  // Constructor: Initializes the opcode tables.
  InstructionDecoder();

  // Decodes a single instruction from the provided buffer.
  // @param addr The memory address of the instruction.
  // @param buffer Pointer to the byte stream.
  // @param bufferSize The size of the buffer.
  // @param instr The DecodedInstruction struct to be filled.
  // @return DecoderErrorInfo containing success or failure info.
  DecoderErrorInfo Decode(uint64_t addr, const uint8_t *buffer,
                          size_t bufferSize, DecodedInstruction &instr);

  // Retrieves the statistics of decoded instructions.
  const std::unordered_map<InstructionType, uint64_t> &GetStats() const;

  // Resets the decoding statistics.
  void ResetStats();

private:
  // Internal structure to hold information about an opcode.
  struct OpcodeInfo {
    InstructionType type;
    uint8_t operandCount;
    std::array<DecodedInstruction::Operand::Type, 4> operandTypes;
  };

  // --- Initialization Methods ---
  void InitializeSingleByteOpcodes();
  void InitializeTwoByteOpcodes();
  void InitializeThreeByteOpcodes();
  void InitializeVEXOpcodes();
  void InitializeEVEXOpcodes();
  void InitializeFPUOpcodes();
  void InitializeGroupOpcodes();

  // --- Lazy Initialization Methods ---
  void EnsureSingleByteInitialized() const;
  void EnsureTwoByteInitialized() const;
  void EnsureThreeByteInitialized() const;
  void EnsureVEXInitialized() const;
  void EnsureEVEXInitialized() const;
  void EnsureFPUInitialized() const;
  void EnsureGroupInitialized() const;

  // --- Opcode Table Adders ---
  void AddOpcode(uint8_t opcode, InstructionType type, uint8_t operandCount,
                 std::array<DecodedInstruction::Operand::Type, 4> types);
  void AddTwoByteOpcode(uint8_t opcode, InstructionType type,
                        uint8_t operandCount,
                        std::array<DecodedInstruction::Operand::Type, 4> types);
  void
  AddThreeByteOpcode(uint16_t opcode, InstructionType type,
                     uint8_t operandCount,
                     std::array<DecodedInstruction::Operand::Type, 4> types);

  // --- Parsing Helper Methods ---
  void ParsePrefixes(const uint8_t *&buffer, size_t &remaining,
                     DecodedInstruction &instr);
  void ParseOpcode(const uint8_t *&buffer, size_t &remaining,
                   DecodedInstruction &instr);
  void ParseTwoByteOpcode(const uint8_t *&buffer, size_t &remaining,
                          DecodedInstruction &instr);
  void ParseThreeByteOpcode(const uint8_t *&buffer, size_t &remaining,
                            DecodedInstruction &instr, uint8_t secondByte);
  void ParseVEX(const uint8_t *&buffer, size_t &remaining,
                DecodedInstruction &instr);
  void ParseEVEX(const uint8_t *&buffer, size_t &remaining,
                 DecodedInstruction &instr);
  void ParseVEXInstruction(const uint8_t *&buffer, size_t &remaining,
                           DecodedInstruction &instr);
  void ParseEVEXInstruction(const uint8_t *&buffer, size_t &remaining,
                            DecodedInstruction &instr);
  void ParseFPUInstruction(const uint8_t *&buffer, size_t &remaining,
                           DecodedInstruction &instr);
  void ParseGroupInstruction(const uint8_t *&buffer, size_t &remaining,
                             DecodedInstruction &instr);
  void ParseModRM(const uint8_t *&buffer, size_t &remaining,
                  DecodedInstruction &instr, uint8_t modrmByte);
  void ParseMemoryOperand(const uint8_t *&buffer, size_t &remaining,
                          DecodedInstruction &instr, uint8_t mod, uint8_t rm,
                          uint8_t rexX);
  void ParseImmediateOperands(const uint8_t *&buffer, size_t &remaining,
                              DecodedInstruction &instr);

  // --- Utility Methods ---
  Register ResolveRegister(uint8_t baseRegField, uint8_t rexExtBit);
  bool IsValidRegister(Register reg) const;
  bool IsGroupOpcode(uint8_t opcode);
  bool NeedsModRMParsing(uint8_t opcode);
  bool NeedsTwoByteModRMParsing(uint8_t secondByte);
  bool HasImmediateOperand(uint8_t opcode);
  bool HasTwoByteImmediateOperand(uint8_t secondByte);
  bool HasThreeByteImmediateOperand(uint16_t opcodeKey);
  bool HasVEXImmediateOperand(uint16_t opcodeKey);
  bool HasEVEXImmediateOperand(uint16_t opcodeKey);
  bool HasGroupImmediateOperand(uint16_t groupKey);

  // --- Member Variables (Opcode Tables) ---
  std::unordered_map<uint8_t, OpcodeInfo> singleByteOpcodes;
  std::unordered_map<uint8_t, OpcodeInfo> twoByteOpcodes;
  std::unordered_map<uint16_t, OpcodeInfo> threeByteOpcodes;
  std::unordered_map<uint16_t, OpcodeInfo> vexOpcodes;
  std::unordered_map<uint16_t, OpcodeInfo> evexOpcodes;
  std::unordered_map<uint16_t, OpcodeInfo> fpuOpcodes;
  std::unordered_map<uint16_t, OpcodeInfo> groupOpcodes;

  // Lazy initialization flags
  mutable bool singleByteInitialized = false;
  mutable bool twoByteInitialized = false;
  mutable bool threeByteInitialized = false;
  mutable bool vexInitialized = false;
  mutable bool evexInitialized = false;
  mutable bool fpuInitialized = false;
  mutable bool groupInitialized = false;

  // Decoding statistics
  std::unordered_map<InstructionType, uint64_t> m_stats;
};

} // namespace x86_64

#endif // CPU_INSTRUCTION_DECODER_H